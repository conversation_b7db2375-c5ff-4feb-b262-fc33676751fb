import Link from 'next/link'
import { getAllPosts, getFeaturedPosts, getAllCategories } from '@/lib/content-manager'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

export default function HomePage() {
  const featuredPosts = getFeaturedPosts(6)
  const recentPosts = getAllPosts().slice(0, 8)
  const categories = getAllCategories().slice(0, 6)

  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 py-20">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
            Tech Tutorial Hub
          </h1>
          <p className="text-xl md:text-2xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto">
            专业的技术教程平台，提供高质量的编程指南、开发技巧和最佳实践
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg">
              <Link href="/tutorials">
                浏览教程
              </Link>
            </Button>
            <Button variant="outline" size="lg" asChild>
              <Link href="/about">
                了解更多
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Featured Posts */}
      {featuredPosts.length > 0 && (
        <section className="py-16 bg-white dark:bg-gray-900">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                精选教程
              </h2>
              <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                我们精心挑选的高质量技术教程，帮助您快速掌握最新技术
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {featuredPosts.map((post) => (
                <Card key={post.slug} className="group hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-center justify-between mb-2">
                      <Badge variant="secondary">
                        {post.metadata.category}
                      </Badge>
                      {post.metadata.difficulty && (
                        <span className="text-sm text-gray-500 dark:text-gray-400">
                          {post.metadata.difficulty === 'beginner' && '🟢 初级'}
                          {post.metadata.difficulty === 'intermediate' && '🟡 中级'}
                          {post.metadata.difficulty === 'advanced' && '🔴 高级'}
                        </span>
                      )}
                    </div>
                    <CardTitle className="group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                      <Link href={`/posts/${post.slug}`}>
                        {post.metadata.title}
                      </Link>
                    </CardTitle>
                    <CardDescription>
                      {post.metadata.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                      <span>{post.metadata.readTime || `${post.readingTime}分钟阅读`}</span>
                      <span>{new Date(post.metadata.date).toLocaleDateString('zh-CN')}</span>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Categories */}
      <section className="py-16 bg-gray-50 dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              技术分类
            </h2>
            <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              按技术领域浏览教程，找到您感兴趣的内容
            </p>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {categories.map((category) => (
              <Link
                key={category.name}
                href={`/tutorials?category=${encodeURIComponent(category.name)}`}
                className="group"
              >
                <Card className="text-center hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="text-2xl mb-2">
                      {category.name === 'React' && '⚛️'}
                      {category.name === 'Vue' && '💚'}
                      {category.name === 'JavaScript' && '🟨'}
                      {category.name === 'TypeScript' && '🔷'}
                      {category.name === 'Next.js' && '▲'}
                      {category.name === 'Node.js' && '🟢'}
                      {category.name === 'Python' && '🐍'}
                      {category.name === 'Web开发' && '🌐'}
                      {!['React', 'Vue', 'JavaScript', 'TypeScript', 'Next.js', 'Node.js', 'Python', 'Web开发'].includes(category.name) && '📚'}
                    </div>
                    <h3 className="font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                      {category.name}
                    </h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                      {category.count} 篇教程
                    </p>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Recent Posts */}
      <section className="py-16 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              最新教程
            </h2>
            <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              最新发布的技术教程和开发指南
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {recentPosts.map((post) => (
              <Card key={post.slug} className="group hover:shadow-md transition-shadow">
                <CardHeader className="pb-3">
                  <Badge variant="outline" className="w-fit mb-2">
                    {post.metadata.category}
                  </Badge>
                  <CardTitle className="text-lg group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors line-clamp-2">
                    <Link href={`/posts/${post.slug}`}>
                      {post.metadata.title}
                    </Link>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mb-3">
                    {post.metadata.description}
                  </p>
                  <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                    <span>{post.metadata.readTime || `${post.readingTime}分钟`}</span>
                    <span>{new Date(post.metadata.date).toLocaleDateString('zh-CN')}</span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
          
          <div className="text-center mt-12">
            <Button asChild variant="outline" size="lg">
              <Link href="/tutorials">
                查看所有教程
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-blue-600 dark:bg-blue-700">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            开始您的学习之旅
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            加入我们的技术社区，获取最新的教程和开发资源
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg" variant="secondary">
              <Link href="/tutorials">
                浏览教程
              </Link>
            </Button>
            <Button asChild size="lg" variant="outline" className="text-white border-white hover:bg-white hover:text-blue-600">
              <Link href="/contact">
                联系我们
              </Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  )
}
