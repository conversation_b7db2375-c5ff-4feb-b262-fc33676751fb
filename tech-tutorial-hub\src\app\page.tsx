import Link from "next/link"
import { <PERSON><PERSON><PERSON>, BookOpen, Code, Zap } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { siteConfig } from "@/lib/constants"

export default function Home() {
  const features = [
    {
      icon: BookOpen,
      title: "高质量教程",
      description: "精心编写的技术教程，从基础到进阶，帮助你系统学习编程技能"
    },
    {
      icon: Code,
      title: "实战项目",
      description: "通过真实项目案例学习，掌握实际开发中的技巧和最佳实践"
    },
    {
      icon: Zap,
      title: "持续更新",
      description: "跟上技术发展趋势，定期更新内容，确保知识的时效性"
    }
  ]

  const categories = [
    { name: "React", count: 15, color: "bg-blue-500" },
    { name: "<PERSON><PERSON>", count: 12, color: "bg-green-500" },
    { name: "JavaScript", count: 20, color: "bg-yellow-500" },
    { name: "TypeScript", count: 8, color: "bg-blue-600" },
    { name: "Node.js", count: 10, color: "bg-green-600" },
    { name: "Python", count: 6, color: "bg-blue-400" },
  ]

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="container space-y-6 pb-8 pt-6 md:pb-12 md:pt-10 lg:py-32">
        <div className="mx-auto flex max-w-[58rem] flex-col items-center space-y-4 text-center">
          <h1 className="font-bold text-3xl leading-[1.1] sm:text-3xl md:text-6xl">
            欢迎来到 {siteConfig.name}
          </h1>
          <p className="max-w-[42rem] leading-normal text-muted-foreground sm:text-xl sm:leading-8">
            {siteConfig.description}。从基础概念到高级技巧，我们为每个学习阶段的开发者提供优质内容。
          </p>
          <div className="space-x-4">
            <Button size="lg" asChild>
              <Link href="/tutorials">
                开始学习 <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
            <Button variant="outline" size="lg" asChild>
              <Link href="/about">了解更多</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="container space-y-6 bg-slate-50 py-8 dark:bg-transparent md:py-12 lg:py-24">
        <div className="mx-auto flex max-w-[58rem] flex-col items-center space-y-4 text-center">
          <h2 className="font-bold text-3xl leading-[1.1] sm:text-3xl md:text-6xl">
            为什么选择我们
          </h2>
          <p className="max-w-[85%] leading-normal text-muted-foreground sm:text-lg sm:leading-7">
            我们致力于提供最优质的技术学习体验
          </p>
        </div>
        <div className="mx-auto grid justify-center gap-4 sm:grid-cols-2 md:max-w-[64rem] md:grid-cols-3">
          {features.map((feature, index) => (
            <Card key={index} className="relative overflow-hidden">
              <CardHeader>
                <div className="flex items-center space-x-2">
                  <feature.icon className="h-6 w-6 text-primary" />
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-base">
                  {feature.description}
                </CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Categories Section */}
      <section className="container space-y-6 py-8 md:py-12 lg:py-24">
        <div className="mx-auto flex max-w-[58rem] flex-col items-center space-y-4 text-center">
          <h2 className="font-bold text-3xl leading-[1.1] sm:text-3xl md:text-6xl">
            热门分类
          </h2>
          <p className="max-w-[85%] leading-normal text-muted-foreground sm:text-lg sm:leading-7">
            探索我们精心整理的技术分类
          </p>
        </div>
        <div className="mx-auto grid justify-center gap-4 sm:grid-cols-2 md:max-w-[64rem] md:grid-cols-3">
          {categories.map((category, index) => (
            <Card key={index} className="group cursor-pointer transition-all hover:shadow-md">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg group-hover:text-primary transition-colors">
                    {category.name}
                  </CardTitle>
                  <Badge variant="secondary">{category.count} 篇</Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className={`h-1 w-full rounded-full ${category.color} opacity-60 group-hover:opacity-100 transition-opacity`} />
              </CardContent>
            </Card>
          ))}
        </div>
        <div className="mx-auto text-center">
          <Button variant="outline" size="lg" asChild>
            <Link href="/categories">查看所有分类</Link>
          </Button>
        </div>
      </section>

      {/* CTA Section */}
      <section className="container space-y-6 py-8 md:py-12 lg:py-24">
        <div className="mx-auto flex max-w-[58rem] flex-col items-center space-y-4 text-center">
          <h2 className="font-bold text-3xl leading-[1.1] sm:text-3xl md:text-6xl">
            开始你的学习之旅
          </h2>
          <p className="max-w-[85%] leading-normal text-muted-foreground sm:text-lg sm:leading-7">
            立即开始学习，提升你的编程技能
          </p>
          <Button size="lg" asChild>
            <Link href="/tutorials">
              浏览所有教程 <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </div>
      </section>
    </div>
  )
}
