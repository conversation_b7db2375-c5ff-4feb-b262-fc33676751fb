@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply text-gray-900 dark:text-gray-100;
  }
}

/* Code highlighting styles */
.line--highlighted {
  @apply bg-yellow-200/20 border-l-4 border-yellow-400 pl-4;
}

.word--highlighted {
  @apply bg-yellow-200/30 px-1 py-0.5 rounded;
}

/* Anchor links */
.anchor {
  @apply opacity-0 transition-opacity;
}

h1:hover .anchor,
h2:hover .anchor,
h3:hover .anchor,
h4:hover .anchor,
h5:hover .anchor,
h6:hover .anchor {
  @apply opacity-100;
}

/* Prose styles for MDX content */
.prose {
  @apply max-w-none;
}

.prose pre {
  @apply bg-muted border rounded-lg p-4 overflow-x-auto;
}

.prose code {
  @apply bg-muted px-1.5 py-0.5 rounded text-sm;
}

.prose pre code {
  @apply bg-transparent p-0;
}
