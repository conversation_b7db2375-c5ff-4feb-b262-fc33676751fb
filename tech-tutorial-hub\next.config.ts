import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  images: {
    formats: ['image/avif', 'image/webp'],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
    ],
  },
  // 压缩优化
  compress: true,
  // 静态导出优化
  trailingSlash: false,
  // 实验性功能暂时禁用
  // experimental: {
  //   optimizeCss: true,
  //   optimizePackageImports: ['lucide-react'],
  // },
};

export default nextConfig;
