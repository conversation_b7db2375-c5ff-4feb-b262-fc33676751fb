{"name": "tech-tutorial-hub", "version": "0.1.0", "private": true, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "setup": "node setup-check.js", "dev:windows": "set NODE_OPTIONS=--max-old-space-size=4096 && next dev", "build:windows": "set NODE_OPTIONS=--max-old-space-size=4096 && next build", "content": "node scripts/content-workflow.js", "content:analyze": "node scripts/content-workflow.js analyze", "content:status": "node scripts/content-workflow.js status", "content:ideas": "node scripts/content-workflow.js ideas", "deploy:check": "node scripts/deploy-check.js", "deploy:build": "npm run deploy:check && npm run build", "deploy:preview": "npm run build && npm run start", "seo:check": "npm run content:analyze && npm run deploy:check"}, "dependencies": {"autoprefixer": "^10.4.21", "clsx": "^2.1.1", "gray-matter": "^4.0.3", "lucide-react": "^0.468.0", "next": "14.2.15", "react": "^18.2.0", "react-dom": "^18.2.0", "remark": "^15.0.1", "remark-gfm": "^4.0.1", "remark-html": "^16.0.1", "tailwind-merge": "^2.5.4", "zod": "^3.25.67"}, "devDependencies": {"@tailwindcss/postcss": "^4.0.0", "@types/node": "^20.17.9", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "eslint": "^8.57.1", "eslint-config-next": "14.2.15", "tailwindcss": "^4.0.0", "typescript": "^5.6.3"}}