{"name": "tech-tutorial-hub", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "pre-commit": "npm run lint:fix && npm run format && npm run type-check"}, "dependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "next": "15.3.4", "next-mdx-remote": "^5.0.0", "gray-matter": "^4.0.3", "clsx": "^2.1.1", "tailwind-merge": "^2.5.4", "lucide-react": "^0.468.0", "next-themes": "^0.4.4"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "@tailwindcss/typography": "^0.5.15", "eslint": "^9", "eslint-config-next": "15.3.4", "@eslint/eslintrc": "^3", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.8", "rehype-autolink-headings": "^7.1.0", "rehype-pretty-code": "^0.14.0", "rehype-slug": "^6.0.0", "remark-gfm": "^4.0.0"}}