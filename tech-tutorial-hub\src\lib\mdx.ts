import fs from 'fs'
import path from 'path'
import matter from 'gray-matter'
import { serialize } from 'next-mdx-remote/serialize'
import rehypeSlug from 'rehype-slug'
import rehypeAutolinkHeadings from 'rehype-autolink-headings'
import rehypePrettyCode from 'rehype-pretty-code'
import remarkGfm from 'remark-gfm'
import { calculateReadingTime } from './utils'

const contentDirectory = path.join(process.cwd(), 'content')

export interface PostMeta {
  title: string
  description: string
  date: string
  published: boolean
  tags: string[]
  category: string
  author: string
  image?: string
  slug: string
  readingTime: number
}

export async function getPostBySlug(slug: string) {
  const filePath = path.join(contentDirectory, 'posts', `${slug}.mdx`)
  
  if (!fs.existsSync(filePath)) {
    return null
  }
  
  const fileContent = fs.readFileSync(filePath, 'utf8')
  const { data, content } = matter(fileContent)
  
  const mdxSource = await serialize(content, {
    mdxOptions: {
      remarkPlugins: [remarkGfm],
      rehypePlugins: [
        rehypeSlug,
        [
          rehypePrettyCode,
          {
            theme: 'github-dark',
            onVisitLine(node: any) {
              if (node.children.length === 0) {
                node.children = [{ type: 'text', value: ' ' }]
              }
            },
            onVisitHighlightedLine(node: any) {
              node.properties.className.push('line--highlighted')
            },
            onVisitHighlightedWord(node: any) {
              node.properties.className = ['word--highlighted']
            },
          },
        ],
        [
          rehypeAutolinkHeadings,
          {
            properties: {
              className: ['anchor'],
              ariaLabel: 'Link to section',
            },
          },
        ],
      ],
    },
  })

  return {
    meta: {
      ...data,
      slug,
      readingTime: calculateReadingTime(content),
    } as PostMeta,
    source: mdxSource,
  }
}

export function getAllPosts(): PostMeta[] {
  const postsDirectory = path.join(contentDirectory, 'posts')
  
  if (!fs.existsSync(postsDirectory)) {
    return []
  }
  
  const filenames = fs.readdirSync(postsDirectory)
  
  return filenames
    .filter(name => name.endsWith('.mdx'))
    .map(name => {
      const filePath = path.join(postsDirectory, name)
      const fileContent = fs.readFileSync(filePath, 'utf8')
      const { data, content } = matter(fileContent)
      
      return {
        ...data,
        slug: name.replace('.mdx', ''),
        readingTime: calculateReadingTime(content),
      } as PostMeta
    })
    .filter(post => post.published)
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
}

export function getPostsByCategory(category: string): PostMeta[] {
  return getAllPosts().filter(post => post.category === category)
}

export function getAllCategories(): string[] {
  const posts = getAllPosts()
  const categories = posts.map(post => post.category)
  return Array.from(new Set(categories))
}

export function getAllTags(): string[] {
  const posts = getAllPosts()
  const tags = posts.flatMap(post => post.tags)
  return Array.from(new Set(tags))
}
