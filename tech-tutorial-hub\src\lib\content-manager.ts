import fs from 'fs'
import path from 'path'
import matter from 'gray-matter'
import { validatePostMetadata, validatePostContent, checkContentQuality, analyzeSEO, analyzeReadability } from './content-validator'
import type { PostMetadata } from './content-validator'

const CONTENT_DIR = path.join(process.cwd(), 'content')
const POSTS_DIR = path.join(CONTENT_DIR, 'posts')
const DRAFTS_DIR = path.join(CONTENT_DIR, 'drafts')

// Ensure content directories exist
export function ensureContentDirectories() {
  const dirs = [CONTENT_DIR, POSTS_DIR, DRAFTS_DIR]
  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true })
    }
  })
}

// Post interface
export interface Post {
  slug: string
  metadata: PostMetadata
  content: string
  excerpt: string
  readingTime: number
  wordCount: number
  lastModified: Date
}

// Get all posts
export function getAllPosts(includeDrafts = false): Post[] {
  ensureContentDirectories()
  
  const posts: Post[] = []
  const dirs = [POSTS_DIR]
  
  if (includeDrafts) {
    dirs.push(DRAFTS_DIR)
  }

  dirs.forEach(dir => {
    if (fs.existsSync(dir)) {
      const files = fs.readdirSync(dir).filter(file => file.endsWith('.md'))
      
      files.forEach(file => {
        try {
          const post = getPostBySlug(path.basename(file, '.md'), dir === DRAFTS_DIR)
          if (post) {
            posts.push(post)
          }
        } catch (error) {
          console.error(`Error loading post ${file}:`, error)
        }
      })
    }
  })

  return posts.sort((a, b) => new Date(b.metadata.date).getTime() - new Date(a.metadata.date).getTime())
}

// Get post by slug
export function getPostBySlug(slug: string, isDraft = false): Post | null {
  const dir = isDraft ? DRAFTS_DIR : POSTS_DIR
  const filePath = path.join(dir, `${slug}.md`)

  if (!fs.existsSync(filePath)) {
    return null
  }

  try {
    const fileContent = fs.readFileSync(filePath, 'utf8')
    const { data, content } = matter(fileContent)
    const stats = fs.statSync(filePath)

    // Validate metadata
    const metadata = validatePostMetadata(data)

    // Calculate reading time (average 200 words per minute)
    const wordCount = content.split(/\s+/).length
    const readingTime = Math.ceil(wordCount / 200)

    // Generate excerpt
    const excerpt = generateExcerpt(content)

    return {
      slug,
      metadata,
      content,
      excerpt,
      readingTime,
      wordCount,
      lastModified: stats.mtime
    }
  } catch (error) {
    console.error(`Error parsing post ${slug}:`, error)
    return null
  }
}

// Get posts by category
export function getPostsByCategory(category: string, includeDrafts = false): Post[] {
  return getAllPosts(includeDrafts).filter(post => 
    post.metadata.category.toLowerCase() === category.toLowerCase()
  )
}

// Get posts by tag
export function getPostsByTag(tag: string, includeDrafts = false): Post[] {
  return getAllPosts(includeDrafts).filter(post => 
    post.metadata.tags?.some(t => t.toLowerCase() === tag.toLowerCase())
  )
}

// Search posts
export function searchPosts(query: string, includeDrafts = false): Post[] {
  const posts = getAllPosts(includeDrafts)
  const searchTerm = query.toLowerCase()

  return posts.filter(post => {
    const searchableContent = [
      post.metadata.title,
      post.metadata.description,
      post.metadata.category,
      ...(post.metadata.tags || []),
      post.content
    ].join(' ').toLowerCase()

    return searchableContent.includes(searchTerm)
  })
}

// Get featured posts
export function getFeaturedPosts(limit = 3): Post[] {
  return getAllPosts()
    .filter(post => post.metadata.featured)
    .slice(0, limit)
}

// Get recent posts
export function getRecentPosts(limit = 5): Post[] {
  return getAllPosts().slice(0, limit)
}

// Get related posts
export function getRelatedPosts(currentPost: Post, limit = 3): Post[] {
  const allPosts = getAllPosts().filter(post => post.slug !== currentPost.slug)
  
  // Score posts based on similarity
  const scoredPosts = allPosts.map(post => {
    let score = 0
    
    // Same category gets high score
    if (post.metadata.category === currentPost.metadata.category) {
      score += 10
    }
    
    // Shared tags get medium score
    const sharedTags = post.metadata.tags?.filter(tag => 
      currentPost.metadata.tags?.includes(tag)
    ) || []
    score += sharedTags.length * 5
    
    // Same difficulty level gets small score
    if (post.metadata.difficulty === currentPost.metadata.difficulty) {
      score += 2
    }
    
    return { post, score }
  })
  
  return scoredPosts
    .sort((a, b) => b.score - a.score)
    .slice(0, limit)
    .map(item => item.post)
}

// Get all categories
export function getAllCategories(): { name: string; count: number }[] {
  const posts = getAllPosts()
  const categoryCount: Record<string, number> = {}

  posts.forEach(post => {
    const category = post.metadata.category
    categoryCount[category] = (categoryCount[category] || 0) + 1
  })

  return Object.entries(categoryCount)
    .map(([name, count]) => ({ name, count }))
    .sort((a, b) => b.count - a.count)
}

// Get all tags
export function getAllTags(): { name: string; count: number }[] {
  const posts = getAllPosts()
  const tagCount: Record<string, number> = {}

  posts.forEach(post => {
    post.metadata.tags?.forEach(tag => {
      tagCount[tag] = (tagCount[tag] || 0) + 1
    })
  })

  return Object.entries(tagCount)
    .map(([name, count]) => ({ name, count }))
    .sort((a, b) => b.count - a.count)
}

// Create new post
export function createPost(slug: string, metadata: PostMetadata, content: string, isDraft = true): boolean {
  try {
    const dir = isDraft ? DRAFTS_DIR : POSTS_DIR
    const filePath = path.join(dir, `${slug}.md`)

    // Validate content
    validatePostContent({ metadata, content, slug })

    // Create frontmatter
    const frontmatter = matter.stringify(content, metadata)

    // Write file
    fs.writeFileSync(filePath, frontmatter, 'utf8')
    
    return true
  } catch (error) {
    console.error(`Error creating post ${slug}:`, error)
    return false
  }
}

// Update post
export function updatePost(slug: string, metadata: PostMetadata, content: string, isDraft = false): boolean {
  try {
    const dir = isDraft ? DRAFTS_DIR : POSTS_DIR
    const filePath = path.join(dir, `${slug}.md`)

    if (!fs.existsSync(filePath)) {
      return false
    }

    // Validate content
    validatePostContent({ metadata, content, slug })

    // Create frontmatter
    const frontmatter = matter.stringify(content, metadata)

    // Write file
    fs.writeFileSync(filePath, frontmatter, 'utf8')
    
    return true
  } catch (error) {
    console.error(`Error updating post ${slug}:`, error)
    return false
  }
}

// Delete post
export function deletePost(slug: string, isDraft = false): boolean {
  try {
    const dir = isDraft ? DRAFTS_DIR : POSTS_DIR
    const filePath = path.join(dir, `${slug}.md`)

    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath)
      return true
    }
    
    return false
  } catch (error) {
    console.error(`Error deleting post ${slug}:`, error)
    return false
  }
}

// Publish draft
export function publishDraft(slug: string): boolean {
  try {
    const draftPath = path.join(DRAFTS_DIR, `${slug}.md`)
    const publishPath = path.join(POSTS_DIR, `${slug}.md`)

    if (!fs.existsSync(draftPath)) {
      return false
    }

    // Move file from drafts to posts
    fs.renameSync(draftPath, publishPath)
    
    return true
  } catch (error) {
    console.error(`Error publishing draft ${slug}:`, error)
    return false
  }
}

// Generate excerpt from content
function generateExcerpt(content: string, maxLength = 200): string {
  // Remove markdown formatting
  const plainText = content
    .replace(/#{1,6}\s+/g, '') // Remove headers
    .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold
    .replace(/\*(.*?)\*/g, '$1') // Remove italic
    .replace(/`(.*?)`/g, '$1') // Remove inline code
    .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // Remove links
    .replace(/!\[([^\]]*)\]\([^)]+\)/g, '') // Remove images
    .replace(/```[\s\S]*?```/g, '') // Remove code blocks
    .trim()

  if (plainText.length <= maxLength) {
    return plainText
  }

  // Find the last complete sentence within the limit
  const truncated = plainText.substring(0, maxLength)
  const lastSentence = truncated.lastIndexOf('.')
  
  if (lastSentence > maxLength * 0.7) {
    return truncated.substring(0, lastSentence + 1)
  }
  
  // If no good sentence break, find last word
  const lastSpace = truncated.lastIndexOf(' ')
  return truncated.substring(0, lastSpace) + '...'
}

// Content analysis
export function analyzeContent(slug: string, isDraft = false) {
  const post = getPostBySlug(slug, isDraft)
  if (!post) {
    return null
  }

  const qualityAnalysis = checkContentQuality(post.content)
  const seoAnalysis = analyzeSEO(post.metadata.title, post.metadata.description, post.content)
  const readabilityAnalysis = analyzeReadability(post.content)

  return {
    post,
    quality: qualityAnalysis,
    seo: seoAnalysis,
    readability: readabilityAnalysis,
    overall: {
      score: Math.round((qualityAnalysis.score + seoAnalysis.score + readabilityAnalysis.score) / 3),
      recommendations: [
        ...qualityAnalysis.issues,
        ...qualityAnalysis.suggestions,
        ...seoAnalysis.issues,
        ...seoAnalysis.suggestions,
        ...readabilityAnalysis.suggestions
      ]
    }
  }
}

// 获取精选文章
export function getFeaturedPosts(limit?: number): Post[] {
  const posts = getAllPosts()
  const featured = posts.filter(post => post.metadata.featured)
  return limit ? featured.slice(0, limit) : featured
}

// 获取所有分类
export function getAllCategories(): Array<{ name: string; count: number }> {
  const posts = getAllPosts()
  const categoryMap = new Map<string, number>()

  posts.forEach(post => {
    const category = post.metadata.category
    categoryMap.set(category, (categoryMap.get(category) || 0) + 1)
  })

  return Array.from(categoryMap.entries())
    .map(([name, count]) => ({ name, count }))
    .sort((a, b) => b.count - a.count)
}
