/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Csong%5CDocuments%5Caugment-projects%5Cgooglead%5Ctech-tutorial-hub%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csong%5CDocuments%5Caugment-projects%5Cgooglead%5Ctech-tutorial-hub&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Csong%5CDocuments%5Caugment-projects%5Cgooglead%5Ctech-tutorial-hub%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csong%5CDocuments%5Caugment-projects%5Cgooglead%5Ctech-tutorial-hub&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9100\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)),\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/_not-found/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Csong%5CDocuments%5Caugment-projects%5Cgooglead%5Ctech-tutorial-hub%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csong%5CDocuments%5Caugment-projects%5Cgooglead%5Ctech-tutorial-hub&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-sans%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-mono%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Csite-header.tsx%22%2C%22ids%22%3A%5B%22SiteHeader%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Csrc%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-sans%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-mono%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Csite-header.tsx%22%2C%22ids%22%3A%5B%22SiteHeader%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Csrc%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/site-header.tsx */ \"(ssr)/./src/components/layout/site-header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/theme-provider.tsx */ \"(ssr)/./src/components/theme-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-sans%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-mono%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Csite-header.tsx%22%2C%22ids%22%3A%5B%22SiteHeader%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Csrc%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/site-header.tsx":
/*!***********************************************!*\
  !*** ./src/components/layout/site-header.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SiteHeader: () => (/* binding */ SiteHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/theme-provider */ \"(ssr)/./src/components/theme-provider.tsx\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/constants */ \"(ssr)/./src/lib/constants.ts\");\n/* __next_internal_client_entry_do_not_use__ SiteHeader auto */ \n\n\n\n\n\nfunction SiteHeader() {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const { theme, setTheme } = (0,_components_theme_provider__WEBPACK_IMPORTED_MODULE_4__.useTheme)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"sticky top-0 z-50 w-full border-b bg-white/95 backdrop-blur dark:bg-gray-900/95 dark:border-gray-700\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container flex h-16 items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mr-4 hidden md:flex\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: \"/\",\n                                className: \"mr-6 flex items-center space-x-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"hidden font-bold sm:inline-block\",\n                                    children: _lib_constants__WEBPACK_IMPORTED_MODULE_5__.siteConfig.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                                    lineNumber: 21,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"flex items-center space-x-6 text-sm font-medium\",\n                                children: _lib_constants__WEBPACK_IMPORTED_MODULE_5__.navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative group\",\n                                        children: item.href ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: item.href,\n                                            className: \"transition-colors hover:text-foreground/80 text-foreground/60\",\n                                            children: item.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                                            lineNumber: 31,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"transition-colors hover:text-gray-900 text-gray-600 dark:hover:text-gray-100 dark:text-gray-400\",\n                                                    children: item.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                                                    lineNumber: 39,\n                                                    columnNumber: 21\n                                                }, this),\n                                                item.items && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute left-0 top-full mt-2 w-48 rounded-md border bg-white p-2 shadow-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all dark:bg-gray-800 dark:border-gray-600\",\n                                                    children: item.items.map((subItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                            href: subItem.href,\n                                                            className: \"block px-3 py-2 text-sm rounded-sm hover:bg-gray-100 dark:hover:bg-gray-700\",\n                                                            children: subItem.title\n                                                        }, subItem.title, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                                                            lineNumber: 45,\n                                                            columnNumber: 27\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                                                    lineNumber: 43,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, item.title, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                                        lineNumber: 29,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        variant: \"ghost\",\n                        className: \"mr-2 px-0 text-base hover:bg-transparent focus-visible:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 md:hidden\",\n                        onClick: ()=>setIsOpen(!isOpen),\n                        children: [\n                            isOpen ? \"✕\" : \"☰\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"sr-only\",\n                                children: \"Toggle Menu\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-1 items-center justify-between space-x-2 md:justify-end\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full flex-1 md:w-auto md:flex-none\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    className: \"relative h-9 w-full justify-start text-sm text-gray-600 dark:text-gray-400 sm:pr-12 md:w-40 lg:w-64\",\n                                    children: \"\\uD83D\\uDD0D 搜索教程...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: ()=>setTheme(theme === \"light\" ? \"dark\" : \"light\"),\n                                    children: [\n                                        theme === \"light\" ? \"\\uD83C\\uDF19\" : \"☀️\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"sr-only\",\n                                            children: \"Toggle theme\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t md:hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex flex-col space-y-4\",\n                        children: _lib_constants__WEBPACK_IMPORTED_MODULE_5__.navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: item.href ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: item.href,\n                                    className: \"text-sm font-medium\",\n                                    onClick: ()=>setIsOpen(false),\n                                    children: item.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 21\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm font-medium text-muted-foreground\",\n                                            children: item.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 23\n                                        }, this),\n                                        item.items && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4 mt-2 space-y-2\",\n                                            children: item.items.map((subItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    href: subItem.href,\n                                                    className: \"block text-sm\",\n                                                    onClick: ()=>setIsOpen(false),\n                                                    children: subItem.title\n                                                }, subItem.title, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 29\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 25\n                                        }, this)\n                                    ]\n                                }, void 0, true)\n                            }, item.title, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 17\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                lineNumber: 97,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/site-header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/theme-provider.tsx":
/*!*******************************************!*\
  !*** ./src/components/theme-provider.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme auto */ \n\nconst initialState = {\n    theme: \"system\",\n    setTheme: ()=>null\n};\nconst ThemeProviderContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createContext(initialState);\nfunction ThemeProvider({ children, defaultTheme = \"system\", storageKey = \"theme\", attribute = \"class\", enableSystem = true, disableTransitionOnChange = false, ...props }) {\n    const [theme, setTheme] = react__WEBPACK_IMPORTED_MODULE_1__.useState(defaultTheme);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect(()=>{\n        const root = window.document.documentElement;\n        root.classList.remove(\"light\", \"dark\");\n        if (theme === \"system\" && enableSystem) {\n            const systemTheme = window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? \"dark\" : \"light\";\n            root.classList.add(systemTheme);\n            return;\n        }\n        root.classList.add(theme);\n    }, [\n        theme,\n        enableSystem\n    ]);\n    const value = {\n        theme,\n        setTheme: (theme)=>{\n            localStorage.setItem(storageKey, theme);\n            setTheme(theme);\n        }\n    };\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect(()=>{\n        const storedTheme = localStorage.getItem(storageKey);\n        if (storedTheme) {\n            setTheme(storedTheme);\n        }\n    }, [\n        storageKey\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeProviderContext.Provider, {\n        ...props,\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\theme-provider.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\nconst useTheme = ()=>{\n    const context = react__WEBPACK_IMPORTED_MODULE_1__.useContext(ThemeProviderContext);\n    if (context === undefined) throw new Error(\"useTheme must be used within a ThemeProvider\");\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy90aGVtZS1wcm92aWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUU4QjtBQWtCOUIsTUFBTUMsZUFBbUM7SUFDdkNDLE9BQU87SUFDUEMsVUFBVSxJQUFNO0FBQ2xCO0FBRUEsTUFBTUMscUNBQXVCSixnREFBbUIsQ0FBcUJDO0FBRTlELFNBQVNLLGNBQWMsRUFDNUJDLFFBQVEsRUFDUkMsZUFBZSxRQUFRLEVBQ3ZCQyxhQUFhLE9BQU8sRUFDcEJDLFlBQVksT0FBTyxFQUNuQkMsZUFBZSxJQUFJLEVBQ25CQyw0QkFBNEIsS0FBSyxFQUNqQyxHQUFHQyxPQUNnQjtJQUNuQixNQUFNLENBQUNYLE9BQU9DLFNBQVMsR0FBR0gsMkNBQWMsQ0FBUVE7SUFFaERSLDRDQUFlLENBQUM7UUFDZCxNQUFNZ0IsT0FBT0MsT0FBT0MsUUFBUSxDQUFDQyxlQUFlO1FBRTVDSCxLQUFLSSxTQUFTLENBQUNDLE1BQU0sQ0FBQyxTQUFTO1FBRS9CLElBQUluQixVQUFVLFlBQVlTLGNBQWM7WUFDdEMsTUFBTVcsY0FBY0wsT0FBT00sVUFBVSxDQUFDLGdDQUNuQ0MsT0FBTyxHQUNOLFNBQ0E7WUFFSlIsS0FBS0ksU0FBUyxDQUFDSyxHQUFHLENBQUNIO1lBQ25CO1FBQ0Y7UUFFQU4sS0FBS0ksU0FBUyxDQUFDSyxHQUFHLENBQUN2QjtJQUNyQixHQUFHO1FBQUNBO1FBQU9TO0tBQWE7SUFFeEIsTUFBTWUsUUFBUTtRQUNaeEI7UUFDQUMsVUFBVSxDQUFDRDtZQUNUeUIsYUFBYUMsT0FBTyxDQUFDbkIsWUFBWVA7WUFDakNDLFNBQVNEO1FBQ1g7SUFDRjtJQUVBRiw0Q0FBZSxDQUFDO1FBQ2QsTUFBTTZCLGNBQWNGLGFBQWFHLE9BQU8sQ0FBQ3JCO1FBQ3pDLElBQUlvQixhQUFhO1lBQ2YxQixTQUFTMEI7UUFDWDtJQUNGLEdBQUc7UUFBQ3BCO0tBQVc7SUFFZixxQkFDRSw4REFBQ0wscUJBQXFCMkIsUUFBUTtRQUFFLEdBQUdsQixLQUFLO1FBQUVhLE9BQU9BO2tCQUM5Q25COzs7Ozs7QUFHUDtBQUVPLE1BQU15QixXQUFXO0lBQ3RCLE1BQU1DLFVBQVVqQyw2Q0FBZ0IsQ0FBQ0k7SUFFakMsSUFBSTZCLFlBQVlFLFdBQ2QsTUFBTSxJQUFJQyxNQUFNO0lBRWxCLE9BQU9IO0FBQ1QsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL3RlY2gtdHV0b3JpYWwtaHViLy4vc3JjL2NvbXBvbmVudHMvdGhlbWUtcHJvdmlkZXIudHN4P2I2OTYiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcblxudHlwZSBUaGVtZSA9IFwiZGFya1wiIHwgXCJsaWdodFwiIHwgXCJzeXN0ZW1cIlxuXG50eXBlIFRoZW1lUHJvdmlkZXJQcm9wcyA9IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxuICBkZWZhdWx0VGhlbWU/OiBUaGVtZVxuICBzdG9yYWdlS2V5Pzogc3RyaW5nXG4gIGF0dHJpYnV0ZT86IHN0cmluZ1xuICBlbmFibGVTeXN0ZW0/OiBib29sZWFuXG4gIGRpc2FibGVUcmFuc2l0aW9uT25DaGFuZ2U/OiBib29sZWFuXG59XG5cbnR5cGUgVGhlbWVQcm92aWRlclN0YXRlID0ge1xuICB0aGVtZTogVGhlbWVcbiAgc2V0VGhlbWU6ICh0aGVtZTogVGhlbWUpID0+IHZvaWRcbn1cblxuY29uc3QgaW5pdGlhbFN0YXRlOiBUaGVtZVByb3ZpZGVyU3RhdGUgPSB7XG4gIHRoZW1lOiBcInN5c3RlbVwiLFxuICBzZXRUaGVtZTogKCkgPT4gbnVsbCxcbn1cblxuY29uc3QgVGhlbWVQcm92aWRlckNvbnRleHQgPSBSZWFjdC5jcmVhdGVDb250ZXh0PFRoZW1lUHJvdmlkZXJTdGF0ZT4oaW5pdGlhbFN0YXRlKVxuXG5leHBvcnQgZnVuY3Rpb24gVGhlbWVQcm92aWRlcih7XG4gIGNoaWxkcmVuLFxuICBkZWZhdWx0VGhlbWUgPSBcInN5c3RlbVwiLFxuICBzdG9yYWdlS2V5ID0gXCJ0aGVtZVwiLFxuICBhdHRyaWJ1dGUgPSBcImNsYXNzXCIsXG4gIGVuYWJsZVN5c3RlbSA9IHRydWUsXG4gIGRpc2FibGVUcmFuc2l0aW9uT25DaGFuZ2UgPSBmYWxzZSxcbiAgLi4ucHJvcHNcbn06IFRoZW1lUHJvdmlkZXJQcm9wcykge1xuICBjb25zdCBbdGhlbWUsIHNldFRoZW1lXSA9IFJlYWN0LnVzZVN0YXRlPFRoZW1lPihkZWZhdWx0VGhlbWUpXG5cbiAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCByb290ID0gd2luZG93LmRvY3VtZW50LmRvY3VtZW50RWxlbWVudFxuXG4gICAgcm9vdC5jbGFzc0xpc3QucmVtb3ZlKFwibGlnaHRcIiwgXCJkYXJrXCIpXG5cbiAgICBpZiAodGhlbWUgPT09IFwic3lzdGVtXCIgJiYgZW5hYmxlU3lzdGVtKSB7XG4gICAgICBjb25zdCBzeXN0ZW1UaGVtZSA9IHdpbmRvdy5tYXRjaE1lZGlhKFwiKHByZWZlcnMtY29sb3Itc2NoZW1lOiBkYXJrKVwiKVxuICAgICAgICAubWF0Y2hlc1xuICAgICAgICA/IFwiZGFya1wiXG4gICAgICAgIDogXCJsaWdodFwiXG5cbiAgICAgIHJvb3QuY2xhc3NMaXN0LmFkZChzeXN0ZW1UaGVtZSlcbiAgICAgIHJldHVyblxuICAgIH1cblxuICAgIHJvb3QuY2xhc3NMaXN0LmFkZCh0aGVtZSlcbiAgfSwgW3RoZW1lLCBlbmFibGVTeXN0ZW1dKVxuXG4gIGNvbnN0IHZhbHVlID0ge1xuICAgIHRoZW1lLFxuICAgIHNldFRoZW1lOiAodGhlbWU6IFRoZW1lKSA9PiB7XG4gICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShzdG9yYWdlS2V5LCB0aGVtZSlcbiAgICAgIHNldFRoZW1lKHRoZW1lKVxuICAgIH0sXG4gIH1cblxuICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IHN0b3JlZFRoZW1lID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oc3RvcmFnZUtleSkgYXMgVGhlbWVcbiAgICBpZiAoc3RvcmVkVGhlbWUpIHtcbiAgICAgIHNldFRoZW1lKHN0b3JlZFRoZW1lKVxuICAgIH1cbiAgfSwgW3N0b3JhZ2VLZXldKVxuXG4gIHJldHVybiAoXG4gICAgPFRoZW1lUHJvdmlkZXJDb250ZXh0LlByb3ZpZGVyIHsuLi5wcm9wc30gdmFsdWU9e3ZhbHVlfT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L1RoZW1lUHJvdmlkZXJDb250ZXh0LlByb3ZpZGVyPlxuICApXG59XG5cbmV4cG9ydCBjb25zdCB1c2VUaGVtZSA9ICgpID0+IHtcbiAgY29uc3QgY29udGV4dCA9IFJlYWN0LnVzZUNvbnRleHQoVGhlbWVQcm92aWRlckNvbnRleHQpXG5cbiAgaWYgKGNvbnRleHQgPT09IHVuZGVmaW5lZClcbiAgICB0aHJvdyBuZXcgRXJyb3IoXCJ1c2VUaGVtZSBtdXN0IGJlIHVzZWQgd2l0aGluIGEgVGhlbWVQcm92aWRlclwiKVxuXG4gIHJldHVybiBjb250ZXh0XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJpbml0aWFsU3RhdGUiLCJ0aGVtZSIsInNldFRoZW1lIiwiVGhlbWVQcm92aWRlckNvbnRleHQiLCJjcmVhdGVDb250ZXh0IiwiVGhlbWVQcm92aWRlciIsImNoaWxkcmVuIiwiZGVmYXVsdFRoZW1lIiwic3RvcmFnZUtleSIsImF0dHJpYnV0ZSIsImVuYWJsZVN5c3RlbSIsImRpc2FibGVUcmFuc2l0aW9uT25DaGFuZ2UiLCJwcm9wcyIsInVzZVN0YXRlIiwidXNlRWZmZWN0Iiwicm9vdCIsIndpbmRvdyIsImRvY3VtZW50IiwiZG9jdW1lbnRFbGVtZW50IiwiY2xhc3NMaXN0IiwicmVtb3ZlIiwic3lzdGVtVGhlbWUiLCJtYXRjaE1lZGlhIiwibWF0Y2hlcyIsImFkZCIsInZhbHVlIiwibG9jYWxTdG9yYWdlIiwic2V0SXRlbSIsInN0b3JlZFRoZW1lIiwiZ2V0SXRlbSIsIlByb3ZpZGVyIiwidXNlVGhlbWUiLCJjb250ZXh0IiwidXNlQ29udGV4dCIsInVuZGVmaW5lZCIsIkVycm9yIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant = \"default\", size = \"default\", ...props }, ref)=>{\n    const baseClasses = \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\";\n    const variants = {\n        default: \"bg-blue-600 text-white hover:bg-blue-700\",\n        destructive: \"bg-red-600 text-white hover:bg-red-700\",\n        outline: \"border border-gray-300 bg-white hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-800 dark:hover:bg-gray-700\",\n        secondary: \"bg-gray-100 text-gray-900 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-100 dark:hover:bg-gray-600\",\n        ghost: \"hover:bg-gray-100 dark:hover:bg-gray-800\",\n        link: \"text-blue-600 underline-offset-4 hover:underline dark:text-blue-400\"\n    };\n    const sizes = {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(baseClasses, variants[variant], sizes[size], className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 31,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/constants.ts":
/*!******************************!*\
  !*** ./src/lib/constants.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   categories: () => (/* binding */ categories),\n/* harmony export */   navigation: () => (/* binding */ navigation),\n/* harmony export */   siteConfig: () => (/* binding */ siteConfig)\n/* harmony export */ });\nconst siteConfig = {\n    name: \"Tech Tutorial Hub\",\n    description: \"高质量的技术教程和编程指南，帮助开发者提升技能\",\n    url: \"https://tech-tutorial-hub.vercel.app\",\n    ogImage: \"https://tech-tutorial-hub.vercel.app/og.jpg\",\n    links: {\n        github: \"https://github.com/tech-tutorial-hub\"\n    }\n};\nconst navigation = [\n    {\n        title: \"前端开发\",\n        items: [\n            {\n                title: \"React\",\n                href: \"/category/react\"\n            },\n            {\n                title: \"Vue\",\n                href: \"/category/vue\"\n            },\n            {\n                title: \"JavaScript\",\n                href: \"/category/javascript\"\n            },\n            {\n                title: \"TypeScript\",\n                href: \"/category/typescript\"\n            },\n            {\n                title: \"CSS\",\n                href: \"/category/css\"\n            }\n        ]\n    },\n    {\n        title: \"后端开发\",\n        items: [\n            {\n                title: \"Node.js\",\n                href: \"/category/nodejs\"\n            },\n            {\n                title: \"Python\",\n                href: \"/category/python\"\n            },\n            {\n                title: \"数据库\",\n                href: \"/category/database\"\n            },\n            {\n                title: \"API设计\",\n                href: \"/category/api\"\n            }\n        ]\n    },\n    {\n        title: \"工具与部署\",\n        items: [\n            {\n                title: \"Git\",\n                href: \"/category/git\"\n            },\n            {\n                title: \"Docker\",\n                href: \"/category/docker\"\n            },\n            {\n                title: \"CI/CD\",\n                href: \"/category/cicd\"\n            },\n            {\n                title: \"云服务\",\n                href: \"/category/cloud\"\n            }\n        ]\n    },\n    {\n        title: \"实战项目\",\n        href: \"/category/projects\"\n    }\n];\nconst categories = [\n    {\n        name: \"React\",\n        slug: \"react\",\n        description: \"React框架和生态系统\"\n    },\n    {\n        name: \"Vue\",\n        slug: \"vue\",\n        description: \"Vue.js框架和相关技术\"\n    },\n    {\n        name: \"JavaScript\",\n        slug: \"javascript\",\n        description: \"JavaScript语言基础和进阶\"\n    },\n    {\n        name: \"TypeScript\",\n        slug: \"typescript\",\n        description: \"TypeScript类型系统和最佳实践\"\n    },\n    {\n        name: \"CSS\",\n        slug: \"css\",\n        description: \"CSS样式和布局技术\"\n    },\n    {\n        name: \"Node.js\",\n        slug: \"nodejs\",\n        description: \"Node.js后端开发\"\n    },\n    {\n        name: \"Python\",\n        slug: \"python\",\n        description: \"Python编程和应用开发\"\n    },\n    {\n        name: \"数据库\",\n        slug: \"database\",\n        description: \"数据库设计和优化\"\n    },\n    {\n        name: \"Git\",\n        slug: \"git\",\n        description: \"版本控制和协作开发\"\n    },\n    {\n        name: \"Docker\",\n        slug: \"docker\",\n        description: \"容器化和部署技术\"\n    },\n    {\n        name: \"实战项目\",\n        slug: \"projects\",\n        description: \"完整项目开发案例\"\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/constants.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateReadingTime: () => (/* binding */ calculateReadingTime),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   slugify: () => (/* binding */ slugify)\n/* harmony export */ });\n// Simplified utility functions without external dependencies\nfunction cn(...inputs) {\n    return inputs.filter(Boolean).join(\" \");\n}\nfunction formatDate(date) {\n    const dateObj = typeof date === \"string\" ? new Date(date) : date;\n    return dateObj.toLocaleDateString(\"zh-CN\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\"\n    });\n}\nfunction calculateReadingTime(content) {\n    const wordsPerMinute = 200;\n    const wordCount = content.split(/\\s+/).length;\n    return Math.ceil(wordCount / wordsPerMinute);\n}\nfunction slugify(text) {\n    return text.toLowerCase().replace(/[^a-z0-9\\s-]/g, \"\").replace(/\\s+/g, \"-\").trim();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"804a49d5c69d\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGVjaC10dXRvcmlhbC1odWIvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzI2YzQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI4MDRhNDlkNWM2OWRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_sans_variableName_inter___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-sans\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-sans\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_sans_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_sans_variableName_inter___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_mono_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"JetBrains_Mono\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-mono\"}],\"variableName\":\"jetbrainsMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"JetBrains_Mono\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-mono\\\"}],\\\"variableName\\\":\\\"jetbrainsMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_mono_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_mono_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/theme-provider */ \"(rsc)/./src/components/theme-provider.tsx\");\n/* harmony import */ var _components_layout_site_header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/site-header */ \"(rsc)/./src/components/layout/site-header.tsx\");\n/* harmony import */ var _components_layout_site_footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/site-footer */ \"(rsc)/./src/components/layout/site-footer.tsx\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/constants */ \"(rsc)/./src/lib/constants.ts\");\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: {\n        default: _lib_constants__WEBPACK_IMPORTED_MODULE_5__.siteConfig.name,\n        template: `%s - ${_lib_constants__WEBPACK_IMPORTED_MODULE_5__.siteConfig.name}`\n    },\n    description: _lib_constants__WEBPACK_IMPORTED_MODULE_5__.siteConfig.description,\n    keywords: [\n        \"技术教程\",\n        \"编程指南\",\n        \"React\",\n        \"Vue\",\n        \"JavaScript\",\n        \"TypeScript\",\n        \"前端开发\",\n        \"后端开发\"\n    ],\n    authors: [\n        {\n            name: \"Tech Tutorial Hub\",\n            url: _lib_constants__WEBPACK_IMPORTED_MODULE_5__.siteConfig.url\n        }\n    ],\n    creator: \"Tech Tutorial Hub\",\n    openGraph: {\n        type: \"website\",\n        locale: \"zh_CN\",\n        url: _lib_constants__WEBPACK_IMPORTED_MODULE_5__.siteConfig.url,\n        title: _lib_constants__WEBPACK_IMPORTED_MODULE_5__.siteConfig.name,\n        description: _lib_constants__WEBPACK_IMPORTED_MODULE_5__.siteConfig.description,\n        siteName: _lib_constants__WEBPACK_IMPORTED_MODULE_5__.siteConfig.name,\n        images: [\n            {\n                url: _lib_constants__WEBPACK_IMPORTED_MODULE_5__.siteConfig.ogImage,\n                width: 1200,\n                height: 630,\n                alt: _lib_constants__WEBPACK_IMPORTED_MODULE_5__.siteConfig.name\n            }\n        ]\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: _lib_constants__WEBPACK_IMPORTED_MODULE_5__.siteConfig.name,\n        description: _lib_constants__WEBPACK_IMPORTED_MODULE_5__.siteConfig.description,\n        images: [\n            _lib_constants__WEBPACK_IMPORTED_MODULE_5__.siteConfig.ogImage\n        ]\n    },\n    icons: {\n        icon: \"/favicon.ico\",\n        shortcut: \"/favicon-16x16.png\",\n        apple: \"/apple-touch-icon.png\"\n    },\n    manifest: \"/site.webmanifest\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_sans_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_mono_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_7___default().variable)} min-h-screen bg-white dark:bg-gray-900 font-sans antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_provider__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                attribute: \"class\",\n                defaultTheme: \"system\",\n                enableSystem: true,\n                disableTransitionOnChange: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative flex min-h-screen flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_site_header__WEBPACK_IMPORTED_MODULE_3__.SiteHeader, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"flex-1\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_site_footer__WEBPACK_IMPORTED_MODULE_4__.SiteFooter, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 83,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 80,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 79,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFTTUE7QUFLQUM7QUFaaUI7QUFFcUM7QUFDQztBQUNBO0FBQ2hCO0FBWXRDLE1BQU1LLFdBQXFCO0lBQ2hDQyxPQUFPO1FBQ0xDLFNBQVNILHNEQUFVQSxDQUFDSSxJQUFJO1FBQ3hCQyxVQUFVLENBQUMsS0FBSyxFQUFFTCxzREFBVUEsQ0FBQ0ksSUFBSSxDQUFDLENBQUM7SUFDckM7SUFDQUUsYUFBYU4sc0RBQVVBLENBQUNNLFdBQVc7SUFDbkNDLFVBQVU7UUFDUjtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO0tBQ0Q7SUFDREMsU0FBUztRQUNQO1lBQ0VKLE1BQU07WUFDTkssS0FBS1Qsc0RBQVVBLENBQUNTLEdBQUc7UUFDckI7S0FDRDtJQUNEQyxTQUFTO0lBQ1RDLFdBQVc7UUFDVEMsTUFBTTtRQUNOQyxRQUFRO1FBQ1JKLEtBQUtULHNEQUFVQSxDQUFDUyxHQUFHO1FBQ25CUCxPQUFPRixzREFBVUEsQ0FBQ0ksSUFBSTtRQUN0QkUsYUFBYU4sc0RBQVVBLENBQUNNLFdBQVc7UUFDbkNRLFVBQVVkLHNEQUFVQSxDQUFDSSxJQUFJO1FBQ3pCVyxRQUFRO1lBQ047Z0JBQ0VOLEtBQUtULHNEQUFVQSxDQUFDZ0IsT0FBTztnQkFDdkJDLE9BQU87Z0JBQ1BDLFFBQVE7Z0JBQ1JDLEtBQUtuQixzREFBVUEsQ0FBQ0ksSUFBSTtZQUN0QjtTQUNEO0lBQ0g7SUFDQWdCLFNBQVM7UUFDUEMsTUFBTTtRQUNObkIsT0FBT0Ysc0RBQVVBLENBQUNJLElBQUk7UUFDdEJFLGFBQWFOLHNEQUFVQSxDQUFDTSxXQUFXO1FBQ25DUyxRQUFRO1lBQUNmLHNEQUFVQSxDQUFDZ0IsT0FBTztTQUFDO0lBQzlCO0lBQ0FNLE9BQU87UUFDTEMsTUFBTTtRQUNOQyxVQUFVO1FBQ1ZDLE9BQU87SUFDVDtJQUNBQyxVQUFVO0FBQ1osRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO1FBQVFDLHdCQUF3QjtrQkFDekMsNEVBQUNDO1lBQ0NDLFdBQVcsQ0FBQyxFQUFFdEMsaUxBQWMsQ0FBQyxDQUFDLEVBQUVDLGtNQUFzQixDQUFDLDZEQUE2RCxDQUFDO3NCQUVySCw0RUFBQ0MscUVBQWFBO2dCQUNac0MsV0FBVTtnQkFDVkMsY0FBYTtnQkFDYkMsWUFBWTtnQkFDWkMseUJBQXlCOzBCQUV6Qiw0RUFBQ0M7b0JBQUlOLFdBQVU7O3NDQUNiLDhEQUFDbkMsc0VBQVVBOzs7OztzQ0FDWCw4REFBQzBDOzRCQUFLUCxXQUFVO3NDQUFVTDs7Ozs7O3NDQUMxQiw4REFBQzdCLHNFQUFVQTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNdkIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90ZWNoLXR1dG9yaWFsLWh1Yi8uL3NyYy9hcHAvbGF5b3V0LnRzeD81N2E5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiO1xuaW1wb3J0IHsgSW50ZXIsIEpldEJyYWluc19Nb25vIH0gZnJvbSBcIm5leHQvZm9udC9nb29nbGVcIjtcbmltcG9ydCBcIi4vZ2xvYmFscy5jc3NcIjtcblxuaW1wb3J0IHsgVGhlbWVQcm92aWRlciB9IGZyb20gXCJAL2NvbXBvbmVudHMvdGhlbWUtcHJvdmlkZXJcIjtcbmltcG9ydCB7IFNpdGVIZWFkZXIgfSBmcm9tIFwiQC9jb21wb25lbnRzL2xheW91dC9zaXRlLWhlYWRlclwiO1xuaW1wb3J0IHsgU2l0ZUZvb3RlciB9IGZyb20gXCJAL2NvbXBvbmVudHMvbGF5b3V0L3NpdGUtZm9vdGVyXCI7XG5pbXBvcnQgeyBzaXRlQ29uZmlnIH0gZnJvbSBcIkAvbGliL2NvbnN0YW50c1wiO1xuXG5jb25zdCBpbnRlciA9IEludGVyKHtcbiAgc3Vic2V0czogW1wibGF0aW5cIl0sXG4gIHZhcmlhYmxlOiBcIi0tZm9udC1zYW5zXCIsXG59KTtcblxuY29uc3QgamV0YnJhaW5zTW9ubyA9IEpldEJyYWluc19Nb25vKHtcbiAgc3Vic2V0czogW1wibGF0aW5cIl0sXG4gIHZhcmlhYmxlOiBcIi0tZm9udC1tb25vXCIsXG59KTtcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6IHtcbiAgICBkZWZhdWx0OiBzaXRlQ29uZmlnLm5hbWUsXG4gICAgdGVtcGxhdGU6IGAlcyAtICR7c2l0ZUNvbmZpZy5uYW1lfWAsXG4gIH0sXG4gIGRlc2NyaXB0aW9uOiBzaXRlQ29uZmlnLmRlc2NyaXB0aW9uLFxuICBrZXl3b3JkczogW1xuICAgIFwi5oqA5pyv5pWZ56iLXCIsXG4gICAgXCLnvJbnqIvmjIfljZdcIixcbiAgICBcIlJlYWN0XCIsXG4gICAgXCJWdWVcIixcbiAgICBcIkphdmFTY3JpcHRcIixcbiAgICBcIlR5cGVTY3JpcHRcIixcbiAgICBcIuWJjeerr+W8gOWPkVwiLFxuICAgIFwi5ZCO56uv5byA5Y+RXCIsXG4gIF0sXG4gIGF1dGhvcnM6IFtcbiAgICB7XG4gICAgICBuYW1lOiBcIlRlY2ggVHV0b3JpYWwgSHViXCIsXG4gICAgICB1cmw6IHNpdGVDb25maWcudXJsLFxuICAgIH0sXG4gIF0sXG4gIGNyZWF0b3I6IFwiVGVjaCBUdXRvcmlhbCBIdWJcIixcbiAgb3BlbkdyYXBoOiB7XG4gICAgdHlwZTogXCJ3ZWJzaXRlXCIsXG4gICAgbG9jYWxlOiBcInpoX0NOXCIsXG4gICAgdXJsOiBzaXRlQ29uZmlnLnVybCxcbiAgICB0aXRsZTogc2l0ZUNvbmZpZy5uYW1lLFxuICAgIGRlc2NyaXB0aW9uOiBzaXRlQ29uZmlnLmRlc2NyaXB0aW9uLFxuICAgIHNpdGVOYW1lOiBzaXRlQ29uZmlnLm5hbWUsXG4gICAgaW1hZ2VzOiBbXG4gICAgICB7XG4gICAgICAgIHVybDogc2l0ZUNvbmZpZy5vZ0ltYWdlLFxuICAgICAgICB3aWR0aDogMTIwMCxcbiAgICAgICAgaGVpZ2h0OiA2MzAsXG4gICAgICAgIGFsdDogc2l0ZUNvbmZpZy5uYW1lLFxuICAgICAgfSxcbiAgICBdLFxuICB9LFxuICB0d2l0dGVyOiB7XG4gICAgY2FyZDogXCJzdW1tYXJ5X2xhcmdlX2ltYWdlXCIsXG4gICAgdGl0bGU6IHNpdGVDb25maWcubmFtZSxcbiAgICBkZXNjcmlwdGlvbjogc2l0ZUNvbmZpZy5kZXNjcmlwdGlvbixcbiAgICBpbWFnZXM6IFtzaXRlQ29uZmlnLm9nSW1hZ2VdLFxuICB9LFxuICBpY29uczoge1xuICAgIGljb246IFwiL2Zhdmljb24uaWNvXCIsXG4gICAgc2hvcnRjdXQ6IFwiL2Zhdmljb24tMTZ4MTYucG5nXCIsXG4gICAgYXBwbGU6IFwiL2FwcGxlLXRvdWNoLWljb24ucG5nXCIsXG4gIH0sXG4gIG1hbmlmZXN0OiBcIi9zaXRlLndlYm1hbmlmZXN0XCIsXG59O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiBSZWFkb25seTx7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59Pikge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJ6aC1DTlwiIHN1cHByZXNzSHlkcmF0aW9uV2FybmluZz5cbiAgICAgIDxib2R5XG4gICAgICAgIGNsYXNzTmFtZT17YCR7aW50ZXIudmFyaWFibGV9ICR7amV0YnJhaW5zTW9uby52YXJpYWJsZX0gbWluLWgtc2NyZWVuIGJnLXdoaXRlIGRhcms6YmctZ3JheS05MDAgZm9udC1zYW5zIGFudGlhbGlhc2VkYH1cbiAgICAgID5cbiAgICAgICAgPFRoZW1lUHJvdmlkZXJcbiAgICAgICAgICBhdHRyaWJ1dGU9XCJjbGFzc1wiXG4gICAgICAgICAgZGVmYXVsdFRoZW1lPVwic3lzdGVtXCJcbiAgICAgICAgICBlbmFibGVTeXN0ZW1cbiAgICAgICAgICBkaXNhYmxlVHJhbnNpdGlvbk9uQ2hhbmdlXG4gICAgICAgID5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIGZsZXggbWluLWgtc2NyZWVuIGZsZXgtY29sXCI+XG4gICAgICAgICAgICA8U2l0ZUhlYWRlciAvPlxuICAgICAgICAgICAgPG1haW4gY2xhc3NOYW1lPVwiZmxleC0xXCI+e2NoaWxkcmVufTwvbWFpbj5cbiAgICAgICAgICAgIDxTaXRlRm9vdGVyIC8+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvVGhlbWVQcm92aWRlcj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiaW50ZXIiLCJqZXRicmFpbnNNb25vIiwiVGhlbWVQcm92aWRlciIsIlNpdGVIZWFkZXIiLCJTaXRlRm9vdGVyIiwic2l0ZUNvbmZpZyIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZWZhdWx0IiwibmFtZSIsInRlbXBsYXRlIiwiZGVzY3JpcHRpb24iLCJrZXl3b3JkcyIsImF1dGhvcnMiLCJ1cmwiLCJjcmVhdG9yIiwib3BlbkdyYXBoIiwidHlwZSIsImxvY2FsZSIsInNpdGVOYW1lIiwiaW1hZ2VzIiwib2dJbWFnZSIsIndpZHRoIiwiaGVpZ2h0IiwiYWx0IiwidHdpdHRlciIsImNhcmQiLCJpY29ucyIsImljb24iLCJzaG9ydGN1dCIsImFwcGxlIiwibWFuaWZlc3QiLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsInN1cHByZXNzSHlkcmF0aW9uV2FybmluZyIsImJvZHkiLCJjbGFzc05hbWUiLCJ2YXJpYWJsZSIsImF0dHJpYnV0ZSIsImRlZmF1bHRUaGVtZSIsImVuYWJsZVN5c3RlbSIsImRpc2FibGVUcmFuc2l0aW9uT25DaGFuZ2UiLCJkaXYiLCJtYWluIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/site-footer.tsx":
/*!***********************************************!*\
  !*** ./src/components/layout/site-footer.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SiteFooter: () => (/* binding */ SiteFooter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/constants */ \"(rsc)/./src/lib/constants.ts\");\n\n\n\nfunction SiteFooter() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"border-t bg-background\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container py-8 md:py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 gap-8 md:grid-cols-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: _lib_constants__WEBPACK_IMPORTED_MODULE_2__.siteConfig.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                    lineNumber: 11,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: _lib_constants__WEBPACK_IMPORTED_MODULE_2__.siteConfig.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                    lineNumber: 12,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                            lineNumber: 10,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-semibold\",\n                                    children: \"快速链接\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                    lineNumber: 19,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/about\",\n                                                className: \"text-muted-foreground hover:text-foreground\",\n                                                children: \"关于我们\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                                lineNumber: 22,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                            lineNumber: 21,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/contact\",\n                                                className: \"text-muted-foreground hover:text-foreground\",\n                                                children: \"联系我们\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                                lineNumber: 27,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                            lineNumber: 26,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/privacy\",\n                                                className: \"text-muted-foreground hover:text-foreground\",\n                                                children: \"隐私政策\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                                lineNumber: 32,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                            lineNumber: 31,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/terms\",\n                                                className: \"text-muted-foreground hover:text-foreground\",\n                                                children: \"使用条款\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                                lineNumber: 37,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                            lineNumber: 36,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                    lineNumber: 20,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-semibold\",\n                                    children: \"热门分类\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/category/react\",\n                                                className: \"text-muted-foreground hover:text-foreground\",\n                                                children: \"React\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                                lineNumber: 49,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                            lineNumber: 48,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/category/vue\",\n                                                className: \"text-muted-foreground hover:text-foreground\",\n                                                children: \"Vue\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                                lineNumber: 54,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                            lineNumber: 53,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/category/javascript\",\n                                                className: \"text-muted-foreground hover:text-foreground\",\n                                                children: \"JavaScript\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/category/typescript\",\n                                                className: \"text-muted-foreground hover:text-foreground\",\n                                                children: \"TypeScript\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-semibold\",\n                                    children: \"资源\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/tutorials\",\n                                                className: \"text-muted-foreground hover:text-foreground\",\n                                                children: \"所有教程\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/projects\",\n                                                className: \"text-muted-foreground hover:text-foreground\",\n                                                children: \"实战项目\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/tools\",\n                                                className: \"text-muted-foreground hover:text-foreground\",\n                                                children: \"开发工具\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/blog\",\n                                                className: \"text-muted-foreground hover:text-foreground\",\n                                                children: \"技术博客\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 border-t pt-8 text-center text-sm text-muted-foreground\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            \"\\xa9 2024 \",\n                            _lib_constants__WEBPACK_IMPORTED_MODULE_2__.siteConfig.name,\n                            \". All rights reserved.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/layout/site-footer.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/site-header.tsx":
/*!***********************************************!*\
  !*** ./src/components/layout/site-header.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SiteHeader: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\googlead\tech-tutorial-hub\src\components\layout\site-header.tsx#SiteHeader`);


/***/ }),

/***/ "(rsc)/./src/components/theme-provider.tsx":
/*!*******************************************!*\
  !*** ./src/components/theme-provider.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ e0),
/* harmony export */   useTheme: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\googlead\tech-tutorial-hub\src\components\theme-provider.tsx#ThemeProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\googlead\tech-tutorial-hub\src\components\theme-provider.tsx#useTheme`);


/***/ }),

/***/ "(rsc)/./src/lib/constants.ts":
/*!******************************!*\
  !*** ./src/lib/constants.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   categories: () => (/* binding */ categories),\n/* harmony export */   navigation: () => (/* binding */ navigation),\n/* harmony export */   siteConfig: () => (/* binding */ siteConfig)\n/* harmony export */ });\nconst siteConfig = {\n    name: \"Tech Tutorial Hub\",\n    description: \"高质量的技术教程和编程指南，帮助开发者提升技能\",\n    url: \"https://tech-tutorial-hub.vercel.app\",\n    ogImage: \"https://tech-tutorial-hub.vercel.app/og.jpg\",\n    links: {\n        github: \"https://github.com/tech-tutorial-hub\"\n    }\n};\nconst navigation = [\n    {\n        title: \"前端开发\",\n        items: [\n            {\n                title: \"React\",\n                href: \"/category/react\"\n            },\n            {\n                title: \"Vue\",\n                href: \"/category/vue\"\n            },\n            {\n                title: \"JavaScript\",\n                href: \"/category/javascript\"\n            },\n            {\n                title: \"TypeScript\",\n                href: \"/category/typescript\"\n            },\n            {\n                title: \"CSS\",\n                href: \"/category/css\"\n            }\n        ]\n    },\n    {\n        title: \"后端开发\",\n        items: [\n            {\n                title: \"Node.js\",\n                href: \"/category/nodejs\"\n            },\n            {\n                title: \"Python\",\n                href: \"/category/python\"\n            },\n            {\n                title: \"数据库\",\n                href: \"/category/database\"\n            },\n            {\n                title: \"API设计\",\n                href: \"/category/api\"\n            }\n        ]\n    },\n    {\n        title: \"工具与部署\",\n        items: [\n            {\n                title: \"Git\",\n                href: \"/category/git\"\n            },\n            {\n                title: \"Docker\",\n                href: \"/category/docker\"\n            },\n            {\n                title: \"CI/CD\",\n                href: \"/category/cicd\"\n            },\n            {\n                title: \"云服务\",\n                href: \"/category/cloud\"\n            }\n        ]\n    },\n    {\n        title: \"实战项目\",\n        href: \"/category/projects\"\n    }\n];\nconst categories = [\n    {\n        name: \"React\",\n        slug: \"react\",\n        description: \"React框架和生态系统\"\n    },\n    {\n        name: \"Vue\",\n        slug: \"vue\",\n        description: \"Vue.js框架和相关技术\"\n    },\n    {\n        name: \"JavaScript\",\n        slug: \"javascript\",\n        description: \"JavaScript语言基础和进阶\"\n    },\n    {\n        name: \"TypeScript\",\n        slug: \"typescript\",\n        description: \"TypeScript类型系统和最佳实践\"\n    },\n    {\n        name: \"CSS\",\n        slug: \"css\",\n        description: \"CSS样式和布局技术\"\n    },\n    {\n        name: \"Node.js\",\n        slug: \"nodejs\",\n        description: \"Node.js后端开发\"\n    },\n    {\n        name: \"Python\",\n        slug: \"python\",\n        description: \"Python编程和应用开发\"\n    },\n    {\n        name: \"数据库\",\n        slug: \"database\",\n        description: \"数据库设计和优化\"\n    },\n    {\n        name: \"Git\",\n        slug: \"git\",\n        description: \"版本控制和协作开发\"\n    },\n    {\n        name: \"Docker\",\n        slug: \"docker\",\n        description: \"容器化和部署技术\"\n    },\n    {\n        name: \"实战项目\",\n        slug: \"projects\",\n        description: \"完整项目开发案例\"\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/constants.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Csong%5CDocuments%5Caugment-projects%5Cgooglead%5Ctech-tutorial-hub%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csong%5CDocuments%5Caugment-projects%5Cgooglead%5Ctech-tutorial-hub&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();