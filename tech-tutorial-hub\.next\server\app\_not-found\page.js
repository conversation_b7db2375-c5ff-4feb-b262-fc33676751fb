/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Csong%5CDocuments%5Caugment-projects%5Cgooglead%5Ctech-tutorial-hub%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csong%5CDocuments%5Caugment-projects%5Cgooglead%5Ctech-tutorial-hub&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Csong%5CDocuments%5Caugment-projects%5Cgooglead%5Ctech-tutorial-hub%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csong%5CDocuments%5Caugment-projects%5Cgooglead%5Ctech-tutorial-hub&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst notFound0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                notFound0,\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [module1, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module2, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Csong%5CDocuments%5Caugment-projects%5Cgooglead%5Ctech-tutorial-hub%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csong%5CDocuments%5Caugment-projects%5Cgooglead%5Ctech-tutorial-hub&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-sans%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-mono%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Csite-header.tsx%22%2C%22ids%22%3A%5B%22SiteHeader%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Csrc%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-sans%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-mono%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Csite-header.tsx%22%2C%22ids%22%3A%5B%22SiteHeader%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Csrc%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/site-header.tsx */ \"(rsc)/./src/components/layout/site-header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/theme-provider.tsx */ \"(rsc)/./src/components/theme-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-sans%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-mono%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Csite-header.tsx%22%2C%22ids%22%3A%5B%22SiteHeader%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Csrc%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"df4aeafec1d5\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNvbmdcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcZ29vZ2xlYWRcXHRlY2gtdHV0b3JpYWwtaHViXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJkZjRhZWFmZWMxZDVcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_sans_variableName_inter___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-sans\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-sans\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_sans_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_sans_variableName_inter___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_mono_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"JetBrains_Mono\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-mono\"}],\"variableName\":\"jetbrainsMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"JetBrains_Mono\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-mono\\\"}],\\\"variableName\\\":\\\"jetbrainsMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_mono_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_mono_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/theme-provider */ \"(rsc)/./src/components/theme-provider.tsx\");\n/* harmony import */ var _components_layout_site_header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/site-header */ \"(rsc)/./src/components/layout/site-header.tsx\");\n/* harmony import */ var _components_layout_site_footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/site-footer */ \"(rsc)/./src/components/layout/site-footer.tsx\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/constants */ \"(rsc)/./src/lib/constants.ts\");\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: {\n        default: _lib_constants__WEBPACK_IMPORTED_MODULE_5__.siteConfig.name,\n        template: `%s - ${_lib_constants__WEBPACK_IMPORTED_MODULE_5__.siteConfig.name}`\n    },\n    description: _lib_constants__WEBPACK_IMPORTED_MODULE_5__.siteConfig.description,\n    keywords: [\n        \"技术教程\",\n        \"编程指南\",\n        \"React\",\n        \"Vue\",\n        \"JavaScript\",\n        \"TypeScript\",\n        \"前端开发\",\n        \"后端开发\"\n    ],\n    authors: [\n        {\n            name: \"Tech Tutorial Hub\",\n            url: _lib_constants__WEBPACK_IMPORTED_MODULE_5__.siteConfig.url\n        }\n    ],\n    creator: \"Tech Tutorial Hub\",\n    openGraph: {\n        type: \"website\",\n        locale: \"zh_CN\",\n        url: _lib_constants__WEBPACK_IMPORTED_MODULE_5__.siteConfig.url,\n        title: _lib_constants__WEBPACK_IMPORTED_MODULE_5__.siteConfig.name,\n        description: _lib_constants__WEBPACK_IMPORTED_MODULE_5__.siteConfig.description,\n        siteName: _lib_constants__WEBPACK_IMPORTED_MODULE_5__.siteConfig.name,\n        images: [\n            {\n                url: _lib_constants__WEBPACK_IMPORTED_MODULE_5__.siteConfig.ogImage,\n                width: 1200,\n                height: 630,\n                alt: _lib_constants__WEBPACK_IMPORTED_MODULE_5__.siteConfig.name\n            }\n        ]\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: _lib_constants__WEBPACK_IMPORTED_MODULE_5__.siteConfig.name,\n        description: _lib_constants__WEBPACK_IMPORTED_MODULE_5__.siteConfig.description,\n        images: [\n            _lib_constants__WEBPACK_IMPORTED_MODULE_5__.siteConfig.ogImage\n        ]\n    },\n    icons: {\n        icon: \"/favicon.ico\",\n        shortcut: \"/favicon-16x16.png\",\n        apple: \"/apple-touch-icon.png\"\n    },\n    manifest: \"/site.webmanifest\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_sans_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_mono_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_7___default().variable)} min-h-screen bg-background font-sans antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_provider__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                attribute: \"class\",\n                defaultTheme: \"system\",\n                enableSystem: true,\n                disableTransitionOnChange: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative flex min-h-screen flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_site_header__WEBPACK_IMPORTED_MODULE_3__.SiteHeader, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"flex-1\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_site_footer__WEBPACK_IMPORTED_MODULE_4__.SiteFooter, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 83,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 80,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 79,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/site-footer.tsx":
/*!***********************************************!*\
  !*** ./src/components/layout/site-footer.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SiteFooter: () => (/* binding */ SiteFooter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/constants */ \"(rsc)/./src/lib/constants.ts\");\n\n\n\nfunction SiteFooter() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"border-t bg-background\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container py-8 md:py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 gap-8 md:grid-cols-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: _lib_constants__WEBPACK_IMPORTED_MODULE_2__.siteConfig.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                    lineNumber: 11,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: _lib_constants__WEBPACK_IMPORTED_MODULE_2__.siteConfig.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                    lineNumber: 12,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                            lineNumber: 10,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-semibold\",\n                                    children: \"快速链接\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                    lineNumber: 19,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/about\",\n                                                className: \"text-muted-foreground hover:text-foreground\",\n                                                children: \"关于我们\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                                lineNumber: 22,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                            lineNumber: 21,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/contact\",\n                                                className: \"text-muted-foreground hover:text-foreground\",\n                                                children: \"联系我们\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                                lineNumber: 27,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                            lineNumber: 26,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/privacy\",\n                                                className: \"text-muted-foreground hover:text-foreground\",\n                                                children: \"隐私政策\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                                lineNumber: 32,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                            lineNumber: 31,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/terms\",\n                                                className: \"text-muted-foreground hover:text-foreground\",\n                                                children: \"使用条款\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                                lineNumber: 37,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                            lineNumber: 36,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                    lineNumber: 20,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-semibold\",\n                                    children: \"热门分类\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/category/react\",\n                                                className: \"text-muted-foreground hover:text-foreground\",\n                                                children: \"React\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                                lineNumber: 49,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                            lineNumber: 48,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/category/vue\",\n                                                className: \"text-muted-foreground hover:text-foreground\",\n                                                children: \"Vue\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                                lineNumber: 54,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                            lineNumber: 53,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/category/javascript\",\n                                                className: \"text-muted-foreground hover:text-foreground\",\n                                                children: \"JavaScript\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/category/typescript\",\n                                                className: \"text-muted-foreground hover:text-foreground\",\n                                                children: \"TypeScript\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-semibold\",\n                                    children: \"资源\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/tutorials\",\n                                                className: \"text-muted-foreground hover:text-foreground\",\n                                                children: \"所有教程\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/projects\",\n                                                className: \"text-muted-foreground hover:text-foreground\",\n                                                children: \"实战项目\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/tools\",\n                                                className: \"text-muted-foreground hover:text-foreground\",\n                                                children: \"开发工具\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/blog\",\n                                                className: \"text-muted-foreground hover:text-foreground\",\n                                                children: \"技术博客\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 border-t pt-8 text-center text-sm text-muted-foreground\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            \"\\xa9 2024 \",\n                            _lib_constants__WEBPACK_IMPORTED_MODULE_2__.siteConfig.name,\n                            \". All rights reserved.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-footer.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvc2l0ZS1mb290ZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBNEI7QUFDZ0I7QUFFckMsU0FBU0U7SUFDZCxxQkFDRSw4REFBQ0M7UUFBT0MsV0FBVTtrQkFDaEIsNEVBQUNDO1lBQUlELFdBQVU7OzhCQUNiLDhEQUFDQztvQkFBSUQsV0FBVTs7c0NBRWIsOERBQUNDOzRCQUFJRCxXQUFVOzs4Q0FDYiw4REFBQ0U7b0NBQUdGLFdBQVU7OENBQXlCSCxzREFBVUEsQ0FBQ00sSUFBSTs7Ozs7OzhDQUN0RCw4REFBQ0M7b0NBQUVKLFdBQVU7OENBQ1ZILHNEQUFVQSxDQUFDUSxXQUFXOzs7Ozs7Ozs7Ozs7c0NBSzNCLDhEQUFDSjs0QkFBSUQsV0FBVTs7OENBQ2IsOERBQUNNO29DQUFHTixXQUFVOzhDQUF3Qjs7Ozs7OzhDQUN0Qyw4REFBQ087b0NBQUdQLFdBQVU7O3NEQUNaLDhEQUFDUTtzREFDQyw0RUFBQ1osa0RBQUlBO2dEQUFDYSxNQUFLO2dEQUFTVCxXQUFVOzBEQUE4Qzs7Ozs7Ozs7Ozs7c0RBSTlFLDhEQUFDUTtzREFDQyw0RUFBQ1osa0RBQUlBO2dEQUFDYSxNQUFLO2dEQUFXVCxXQUFVOzBEQUE4Qzs7Ozs7Ozs7Ozs7c0RBSWhGLDhEQUFDUTtzREFDQyw0RUFBQ1osa0RBQUlBO2dEQUFDYSxNQUFLO2dEQUFXVCxXQUFVOzBEQUE4Qzs7Ozs7Ozs7Ozs7c0RBSWhGLDhEQUFDUTtzREFDQyw0RUFBQ1osa0RBQUlBO2dEQUFDYSxNQUFLO2dEQUFTVCxXQUFVOzBEQUE4Qzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBUWxGLDhEQUFDQzs0QkFBSUQsV0FBVTs7OENBQ2IsOERBQUNNO29DQUFHTixXQUFVOzhDQUF3Qjs7Ozs7OzhDQUN0Qyw4REFBQ087b0NBQUdQLFdBQVU7O3NEQUNaLDhEQUFDUTtzREFDQyw0RUFBQ1osa0RBQUlBO2dEQUFDYSxNQUFLO2dEQUFrQlQsV0FBVTswREFBOEM7Ozs7Ozs7Ozs7O3NEQUl2Riw4REFBQ1E7c0RBQ0MsNEVBQUNaLGtEQUFJQTtnREFBQ2EsTUFBSztnREFBZ0JULFdBQVU7MERBQThDOzs7Ozs7Ozs7OztzREFJckYsOERBQUNRO3NEQUNDLDRFQUFDWixrREFBSUE7Z0RBQUNhLE1BQUs7Z0RBQXVCVCxXQUFVOzBEQUE4Qzs7Ozs7Ozs7Ozs7c0RBSTVGLDhEQUFDUTtzREFDQyw0RUFBQ1osa0RBQUlBO2dEQUFDYSxNQUFLO2dEQUF1QlQsV0FBVTswREFBOEM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQVFoRyw4REFBQ0M7NEJBQUlELFdBQVU7OzhDQUNiLDhEQUFDTTtvQ0FBR04sV0FBVTs4Q0FBd0I7Ozs7Ozs4Q0FDdEMsOERBQUNPO29DQUFHUCxXQUFVOztzREFDWiw4REFBQ1E7c0RBQ0MsNEVBQUNaLGtEQUFJQTtnREFBQ2EsTUFBSztnREFBYVQsV0FBVTswREFBOEM7Ozs7Ozs7Ozs7O3NEQUlsRiw4REFBQ1E7c0RBQ0MsNEVBQUNaLGtEQUFJQTtnREFBQ2EsTUFBSztnREFBWVQsV0FBVTswREFBOEM7Ozs7Ozs7Ozs7O3NEQUlqRiw4REFBQ1E7c0RBQ0MsNEVBQUNaLGtEQUFJQTtnREFBQ2EsTUFBSztnREFBU1QsV0FBVTswREFBOEM7Ozs7Ozs7Ozs7O3NEQUk5RSw4REFBQ1E7c0RBQ0MsNEVBQUNaLGtEQUFJQTtnREFBQ2EsTUFBSztnREFBUVQsV0FBVTswREFBOEM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQVFuRiw4REFBQ0M7b0JBQUlELFdBQVU7OEJBQ2IsNEVBQUNJOzs0QkFBRTs0QkFDT1Asc0RBQVVBLENBQUNNLElBQUk7NEJBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTXBDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNvbmdcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcZ29vZ2xlYWRcXHRlY2gtdHV0b3JpYWwtaHViXFxzcmNcXGNvbXBvbmVudHNcXGxheW91dFxcc2l0ZS1mb290ZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBMaW5rIGZyb20gXCJuZXh0L2xpbmtcIlxuaW1wb3J0IHsgc2l0ZUNvbmZpZyB9IGZyb20gXCJAL2xpYi9jb25zdGFudHNcIlxuXG5leHBvcnQgZnVuY3Rpb24gU2l0ZUZvb3RlcigpIHtcbiAgcmV0dXJuIChcbiAgICA8Zm9vdGVyIGNsYXNzTmFtZT1cImJvcmRlci10IGJnLWJhY2tncm91bmRcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyIHB5LTggbWQ6cHktMTJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIGdhcC04IG1kOmdyaWQtY29scy00XCI+XG4gICAgICAgICAgey8qIEJyYW5kICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkXCI+e3NpdGVDb25maWcubmFtZX08L2gzPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cbiAgICAgICAgICAgICAge3NpdGVDb25maWcuZGVzY3JpcHRpb259XG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgXG4gICAgICAgICAgey8qIFF1aWNrIExpbmtzICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LXNlbWlib2xkXCI+5b+r6YCf6ZO+5o6lPC9oND5cbiAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJzcGFjZS15LTIgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICA8bGk+XG4gICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9hYm91dFwiIGNsYXNzTmFtZT1cInRleHQtbXV0ZWQtZm9yZWdyb3VuZCBob3Zlcjp0ZXh0LWZvcmVncm91bmRcIj5cbiAgICAgICAgICAgICAgICAgIOWFs+S6juaIkeS7rFxuICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgPGxpPlxuICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvY29udGFjdFwiIGNsYXNzTmFtZT1cInRleHQtbXV0ZWQtZm9yZWdyb3VuZCBob3Zlcjp0ZXh0LWZvcmVncm91bmRcIj5cbiAgICAgICAgICAgICAgICAgIOiBlOezu+aIkeS7rFxuICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgPGxpPlxuICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvcHJpdmFjeVwiIGNsYXNzTmFtZT1cInRleHQtbXV0ZWQtZm9yZWdyb3VuZCBob3Zlcjp0ZXh0LWZvcmVncm91bmRcIj5cbiAgICAgICAgICAgICAgICAgIOmakOengeaUv+etllxuICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgPGxpPlxuICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvdGVybXNcIiBjbGFzc05hbWU9XCJ0ZXh0LW11dGVkLWZvcmVncm91bmQgaG92ZXI6dGV4dC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICAgICAgICDkvb/nlKjmnaHmrL5cbiAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICA8L3VsPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIFxuICAgICAgICAgIHsvKiBDYXRlZ29yaWVzICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LXNlbWlib2xkXCI+54Ot6Zeo5YiG57G7PC9oND5cbiAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJzcGFjZS15LTIgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICA8bGk+XG4gICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9jYXRlZ29yeS9yZWFjdFwiIGNsYXNzTmFtZT1cInRleHQtbXV0ZWQtZm9yZWdyb3VuZCBob3Zlcjp0ZXh0LWZvcmVncm91bmRcIj5cbiAgICAgICAgICAgICAgICAgIFJlYWN0XG4gICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICA8L2xpPlxuICAgICAgICAgICAgICA8bGk+XG4gICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9jYXRlZ29yeS92dWVcIiBjbGFzc05hbWU9XCJ0ZXh0LW11dGVkLWZvcmVncm91bmQgaG92ZXI6dGV4dC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICAgICAgICBWdWVcbiAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgIDxsaT5cbiAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL2NhdGVnb3J5L2phdmFzY3JpcHRcIiBjbGFzc05hbWU9XCJ0ZXh0LW11dGVkLWZvcmVncm91bmQgaG92ZXI6dGV4dC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICAgICAgICBKYXZhU2NyaXB0XG4gICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICA8L2xpPlxuICAgICAgICAgICAgICA8bGk+XG4gICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9jYXRlZ29yeS90eXBlc2NyaXB0XCIgY2xhc3NOYW1lPVwidGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGhvdmVyOnRleHQtZm9yZWdyb3VuZFwiPlxuICAgICAgICAgICAgICAgICAgVHlwZVNjcmlwdFxuICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgIDwvdWw+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgXG4gICAgICAgICAgey8qIFJlc291cmNlcyAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1zZW1pYm9sZFwiPui1hOa6kDwvaDQ+XG4gICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwic3BhY2UteS0yIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgPGxpPlxuICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvdHV0b3JpYWxzXCIgY2xhc3NOYW1lPVwidGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGhvdmVyOnRleHQtZm9yZWdyb3VuZFwiPlxuICAgICAgICAgICAgICAgICAg5omA5pyJ5pWZ56iLXG4gICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICA8L2xpPlxuICAgICAgICAgICAgICA8bGk+XG4gICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9wcm9qZWN0c1wiIGNsYXNzTmFtZT1cInRleHQtbXV0ZWQtZm9yZWdyb3VuZCBob3Zlcjp0ZXh0LWZvcmVncm91bmRcIj5cbiAgICAgICAgICAgICAgICAgIOWunuaImOmhueebrlxuICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgPGxpPlxuICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvdG9vbHNcIiBjbGFzc05hbWU9XCJ0ZXh0LW11dGVkLWZvcmVncm91bmQgaG92ZXI6dGV4dC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICAgICAgICDlvIDlj5Hlt6XlhbdcbiAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgIDxsaT5cbiAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL2Jsb2dcIiBjbGFzc05hbWU9XCJ0ZXh0LW11dGVkLWZvcmVncm91bmQgaG92ZXI6dGV4dC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICAgICAgICDmioDmnK/ljZrlrqJcbiAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICA8L3VsPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAgXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtOCBib3JkZXItdCBwdC04IHRleHQtY2VudGVyIHRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgPHA+XG4gICAgICAgICAgICDCqSAyMDI0IHtzaXRlQ29uZmlnLm5hbWV9LiBBbGwgcmlnaHRzIHJlc2VydmVkLlxuICAgICAgICAgIDwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Zvb3Rlcj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIkxpbmsiLCJzaXRlQ29uZmlnIiwiU2l0ZUZvb3RlciIsImZvb3RlciIsImNsYXNzTmFtZSIsImRpdiIsImgzIiwibmFtZSIsInAiLCJkZXNjcmlwdGlvbiIsImg0IiwidWwiLCJsaSIsImhyZWYiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/components/layout/site-footer.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/site-header.tsx":
/*!***********************************************!*\
  !*** ./src/components/layout/site-header.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SiteHeader: () => (/* binding */ SiteHeader)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const SiteHeader = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SiteHeader() from the server but SiteHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\googlead\\tech-tutorial-hub\\src\\components\\layout\\site-header.tsx",
"SiteHeader",
);

/***/ }),

/***/ "(rsc)/./src/components/theme-provider.tsx":
/*!*******************************************!*\
  !*** ./src/components/theme-provider.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),
/* harmony export */   useTheme: () => (/* binding */ useTheme)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ThemeProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\googlead\\tech-tutorial-hub\\src\\components\\theme-provider.tsx",
"ThemeProvider",
);const useTheme = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\googlead\\tech-tutorial-hub\\src\\components\\theme-provider.tsx",
"useTheme",
);

/***/ }),

/***/ "(rsc)/./src/lib/constants.ts":
/*!******************************!*\
  !*** ./src/lib/constants.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   categories: () => (/* binding */ categories),\n/* harmony export */   navigation: () => (/* binding */ navigation),\n/* harmony export */   siteConfig: () => (/* binding */ siteConfig)\n/* harmony export */ });\nconst siteConfig = {\n    name: \"Tech Tutorial Hub\",\n    description: \"高质量的技术教程和编程指南，帮助开发者提升技能\",\n    url: \"https://tech-tutorial-hub.vercel.app\",\n    ogImage: \"https://tech-tutorial-hub.vercel.app/og.jpg\",\n    links: {\n        github: \"https://github.com/tech-tutorial-hub\"\n    }\n};\nconst navigation = [\n    {\n        title: \"前端开发\",\n        items: [\n            {\n                title: \"React\",\n                href: \"/category/react\"\n            },\n            {\n                title: \"Vue\",\n                href: \"/category/vue\"\n            },\n            {\n                title: \"JavaScript\",\n                href: \"/category/javascript\"\n            },\n            {\n                title: \"TypeScript\",\n                href: \"/category/typescript\"\n            },\n            {\n                title: \"CSS\",\n                href: \"/category/css\"\n            }\n        ]\n    },\n    {\n        title: \"后端开发\",\n        items: [\n            {\n                title: \"Node.js\",\n                href: \"/category/nodejs\"\n            },\n            {\n                title: \"Python\",\n                href: \"/category/python\"\n            },\n            {\n                title: \"数据库\",\n                href: \"/category/database\"\n            },\n            {\n                title: \"API设计\",\n                href: \"/category/api\"\n            }\n        ]\n    },\n    {\n        title: \"工具与部署\",\n        items: [\n            {\n                title: \"Git\",\n                href: \"/category/git\"\n            },\n            {\n                title: \"Docker\",\n                href: \"/category/docker\"\n            },\n            {\n                title: \"CI/CD\",\n                href: \"/category/cicd\"\n            },\n            {\n                title: \"云服务\",\n                href: \"/category/cloud\"\n            }\n        ]\n    },\n    {\n        title: \"实战项目\",\n        href: \"/category/projects\"\n    }\n];\nconst categories = [\n    {\n        name: \"React\",\n        slug: \"react\",\n        description: \"React框架和生态系统\"\n    },\n    {\n        name: \"Vue\",\n        slug: \"vue\",\n        description: \"Vue.js框架和相关技术\"\n    },\n    {\n        name: \"JavaScript\",\n        slug: \"javascript\",\n        description: \"JavaScript语言基础和进阶\"\n    },\n    {\n        name: \"TypeScript\",\n        slug: \"typescript\",\n        description: \"TypeScript类型系统和最佳实践\"\n    },\n    {\n        name: \"CSS\",\n        slug: \"css\",\n        description: \"CSS样式和布局技术\"\n    },\n    {\n        name: \"Node.js\",\n        slug: \"nodejs\",\n        description: \"Node.js后端开发\"\n    },\n    {\n        name: \"Python\",\n        slug: \"python\",\n        description: \"Python编程和应用开发\"\n    },\n    {\n        name: \"数据库\",\n        slug: \"database\",\n        description: \"数据库设计和优化\"\n    },\n    {\n        name: \"Git\",\n        slug: \"git\",\n        description: \"版本控制和协作开发\"\n    },\n    {\n        name: \"Docker\",\n        slug: \"docker\",\n        description: \"容器化和部署技术\"\n    },\n    {\n        name: \"实战项目\",\n        slug: \"projects\",\n        description: \"完整项目开发案例\"\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/constants.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-sans%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-mono%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Csite-header.tsx%22%2C%22ids%22%3A%5B%22SiteHeader%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Csrc%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-sans%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-mono%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Csite-header.tsx%22%2C%22ids%22%3A%5B%22SiteHeader%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Csrc%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/site-header.tsx */ \"(ssr)/./src/components/layout/site-header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/theme-provider.tsx */ \"(ssr)/./src/components/theme-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-sans%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-mono%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Csite-header.tsx%22%2C%22ids%22%3A%5B%22SiteHeader%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Csrc%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong%5C%5CDocuments%5C%5Caugment-projects%5C%5Cgooglead%5C%5Ctech-tutorial-hub%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/site-header.tsx":
/*!***********************************************!*\
  !*** ./src/components/layout/site-header.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SiteHeader: () => (/* binding */ SiteHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/theme-provider */ \"(ssr)/./src/components/theme-provider.tsx\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/constants */ \"(ssr)/./src/lib/constants.ts\");\n/* __next_internal_client_entry_do_not_use__ SiteHeader auto */ \n\n\n\n\n\nfunction SiteHeader() {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const { theme, setTheme } = (0,_components_theme_provider__WEBPACK_IMPORTED_MODULE_4__.useTheme)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container flex h-16 items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mr-4 hidden md:flex\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/\",\n                                className: \"mr-6 flex items-center space-x-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"hidden font-bold sm:inline-block\",\n                                    children: _lib_constants__WEBPACK_IMPORTED_MODULE_5__.siteConfig.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                                    lineNumber: 21,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"flex items-center space-x-6 text-sm font-medium\",\n                                children: _lib_constants__WEBPACK_IMPORTED_MODULE_5__.navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative group\",\n                                        children: item.href ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: item.href,\n                                            className: \"transition-colors hover:text-foreground/80 text-foreground/60\",\n                                            children: item.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                                            lineNumber: 31,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"transition-colors hover:text-foreground/80 text-foreground/60\",\n                                                    children: item.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                                                    lineNumber: 39,\n                                                    columnNumber: 21\n                                                }, this),\n                                                item.items && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute left-0 top-full mt-2 w-48 rounded-md border bg-popover p-2 shadow-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all\",\n                                                    children: item.items.map((subItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                            href: subItem.href,\n                                                            className: \"block px-3 py-2 text-sm rounded-sm hover:bg-accent hover:text-accent-foreground\",\n                                                            children: subItem.title\n                                                        }, subItem.title, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                                                            lineNumber: 45,\n                                                            columnNumber: 27\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                                                    lineNumber: 43,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, item.title, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                                        lineNumber: 29,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        variant: \"ghost\",\n                        className: \"mr-2 px-0 text-base hover:bg-transparent focus-visible:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 md:hidden\",\n                        onClick: ()=>setIsOpen(!isOpen),\n                        children: [\n                            isOpen ? \"✕\" : \"☰\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"sr-only\",\n                                children: \"Toggle Menu\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-1 items-center justify-between space-x-2 md:justify-end\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full flex-1 md:w-auto md:flex-none\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    className: \"relative h-9 w-full justify-start text-sm text-muted-foreground sm:pr-12 md:w-40 lg:w-64\",\n                                    children: \"\\uD83D\\uDD0D 搜索教程...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: ()=>setTheme(theme === \"light\" ? \"dark\" : \"light\"),\n                                    children: [\n                                        theme === \"light\" ? \"🌙\" : \"☀️\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"sr-only\",\n                                            children: \"Toggle theme\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t md:hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex flex-col space-y-4\",\n                        children: _lib_constants__WEBPACK_IMPORTED_MODULE_5__.navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: item.href ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: item.href,\n                                    className: \"text-sm font-medium\",\n                                    onClick: ()=>setIsOpen(false),\n                                    children: item.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 21\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm font-medium text-muted-foreground\",\n                                            children: item.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 23\n                                        }, this),\n                                        item.items && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4 mt-2 space-y-2\",\n                                            children: item.items.map((subItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: subItem.href,\n                                                    className: \"block text-sm\",\n                                                    onClick: ()=>setIsOpen(false),\n                                                    children: subItem.title\n                                                }, subItem.title, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 29\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 25\n                                        }, this)\n                                    ]\n                                }, void 0, true)\n                            }, item.title, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 17\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n                lineNumber: 97,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\layout\\\\site-header.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/site-header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/theme-provider.tsx":
/*!*******************************************!*\
  !*** ./src/components/theme-provider.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme auto */ \n\nconst initialState = {\n    theme: \"system\",\n    setTheme: ()=>null\n};\nconst ThemeProviderContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createContext(initialState);\nfunction ThemeProvider({ children, defaultTheme = \"system\", storageKey = \"theme\", attribute = \"class\", enableSystem = true, disableTransitionOnChange = false, ...props }) {\n    const [theme, setTheme] = react__WEBPACK_IMPORTED_MODULE_1__.useState(defaultTheme);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"ThemeProvider.useEffect\": ()=>{\n            const root = window.document.documentElement;\n            root.classList.remove(\"light\", \"dark\");\n            if (theme === \"system\" && enableSystem) {\n                const systemTheme = window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? \"dark\" : \"light\";\n                root.classList.add(systemTheme);\n                return;\n            }\n            root.classList.add(theme);\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        theme,\n        enableSystem\n    ]);\n    const value = {\n        theme,\n        setTheme: (theme)=>{\n            localStorage.setItem(storageKey, theme);\n            setTheme(theme);\n        }\n    };\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"ThemeProvider.useEffect\": ()=>{\n            const storedTheme = localStorage.getItem(storageKey);\n            if (storedTheme) {\n                setTheme(storedTheme);\n            }\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        storageKey\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeProviderContext.Provider, {\n        ...props,\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\theme-provider.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\nconst useTheme = ()=>{\n    const context = react__WEBPACK_IMPORTED_MODULE_1__.useContext(ThemeProviderContext);\n    if (context === undefined) throw new Error(\"useTheme must be used within a ThemeProvider\");\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant = \"default\", size = \"default\", ...props }, ref)=>{\n    const baseClasses = \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\";\n    const variants = {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\"\n    };\n    const sizes = {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(baseClasses, variants[variant], sizes[size], className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\googlead\\\\tech-tutorial-hub\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 31,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/constants.ts":
/*!******************************!*\
  !*** ./src/lib/constants.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   categories: () => (/* binding */ categories),\n/* harmony export */   navigation: () => (/* binding */ navigation),\n/* harmony export */   siteConfig: () => (/* binding */ siteConfig)\n/* harmony export */ });\nconst siteConfig = {\n    name: \"Tech Tutorial Hub\",\n    description: \"高质量的技术教程和编程指南，帮助开发者提升技能\",\n    url: \"https://tech-tutorial-hub.vercel.app\",\n    ogImage: \"https://tech-tutorial-hub.vercel.app/og.jpg\",\n    links: {\n        github: \"https://github.com/tech-tutorial-hub\"\n    }\n};\nconst navigation = [\n    {\n        title: \"前端开发\",\n        items: [\n            {\n                title: \"React\",\n                href: \"/category/react\"\n            },\n            {\n                title: \"Vue\",\n                href: \"/category/vue\"\n            },\n            {\n                title: \"JavaScript\",\n                href: \"/category/javascript\"\n            },\n            {\n                title: \"TypeScript\",\n                href: \"/category/typescript\"\n            },\n            {\n                title: \"CSS\",\n                href: \"/category/css\"\n            }\n        ]\n    },\n    {\n        title: \"后端开发\",\n        items: [\n            {\n                title: \"Node.js\",\n                href: \"/category/nodejs\"\n            },\n            {\n                title: \"Python\",\n                href: \"/category/python\"\n            },\n            {\n                title: \"数据库\",\n                href: \"/category/database\"\n            },\n            {\n                title: \"API设计\",\n                href: \"/category/api\"\n            }\n        ]\n    },\n    {\n        title: \"工具与部署\",\n        items: [\n            {\n                title: \"Git\",\n                href: \"/category/git\"\n            },\n            {\n                title: \"Docker\",\n                href: \"/category/docker\"\n            },\n            {\n                title: \"CI/CD\",\n                href: \"/category/cicd\"\n            },\n            {\n                title: \"云服务\",\n                href: \"/category/cloud\"\n            }\n        ]\n    },\n    {\n        title: \"实战项目\",\n        href: \"/category/projects\"\n    }\n];\nconst categories = [\n    {\n        name: \"React\",\n        slug: \"react\",\n        description: \"React框架和生态系统\"\n    },\n    {\n        name: \"Vue\",\n        slug: \"vue\",\n        description: \"Vue.js框架和相关技术\"\n    },\n    {\n        name: \"JavaScript\",\n        slug: \"javascript\",\n        description: \"JavaScript语言基础和进阶\"\n    },\n    {\n        name: \"TypeScript\",\n        slug: \"typescript\",\n        description: \"TypeScript类型系统和最佳实践\"\n    },\n    {\n        name: \"CSS\",\n        slug: \"css\",\n        description: \"CSS样式和布局技术\"\n    },\n    {\n        name: \"Node.js\",\n        slug: \"nodejs\",\n        description: \"Node.js后端开发\"\n    },\n    {\n        name: \"Python\",\n        slug: \"python\",\n        description: \"Python编程和应用开发\"\n    },\n    {\n        name: \"数据库\",\n        slug: \"database\",\n        description: \"数据库设计和优化\"\n    },\n    {\n        name: \"Git\",\n        slug: \"git\",\n        description: \"版本控制和协作开发\"\n    },\n    {\n        name: \"Docker\",\n        slug: \"docker\",\n        description: \"容器化和部署技术\"\n    },\n    {\n        name: \"实战项目\",\n        slug: \"projects\",\n        description: \"完整项目开发案例\"\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/constants.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateReadingTime: () => (/* binding */ calculateReadingTime),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   slugify: () => (/* binding */ slugify)\n/* harmony export */ });\n// Simplified utility functions without external dependencies\nfunction cn(...inputs) {\n    return inputs.filter(Boolean).join(' ');\n}\nfunction formatDate(date) {\n    const dateObj = typeof date === 'string' ? new Date(date) : date;\n    return dateObj.toLocaleDateString('zh-CN', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n    });\n}\nfunction calculateReadingTime(content) {\n    const wordsPerMinute = 200;\n    const wordCount = content.split(/\\s+/).length;\n    return Math.ceil(wordCount / wordsPerMinute);\n}\nfunction slugify(text) {\n    return text.toLowerCase().replace(/[^a-z0-9\\s-]/g, '').replace(/\\s+/g, '-').trim();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Csong%5CDocuments%5Caugment-projects%5Cgooglead%5Ctech-tutorial-hub%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csong%5CDocuments%5Caugment-projects%5Cgooglead%5Ctech-tutorial-hub&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();