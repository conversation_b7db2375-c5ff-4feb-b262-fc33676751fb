import Link from "next/link"
import { siteConfig } from "@/lib/constants"

export function SiteFooter() {
  return (
    <footer className="border-t bg-background">
      <div className="container py-8 md:py-12">
        <div className="grid grid-cols-1 gap-8 md:grid-cols-4">
          {/* Brand */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">{siteConfig.name}</h3>
            <p className="text-sm text-muted-foreground">
              {siteConfig.description}
            </p>
          </div>
          
          {/* Quick Links */}
          <div className="space-y-4">
            <h4 className="text-sm font-semibold">快速链接</h4>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/about" className="text-muted-foreground hover:text-foreground">
                  关于我们
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-muted-foreground hover:text-foreground">
                  联系我们
                </Link>
              </li>
              <li>
                <Link href="/privacy" className="text-muted-foreground hover:text-foreground">
                  隐私政策
                </Link>
              </li>
              <li>
                <Link href="/terms" className="text-muted-foreground hover:text-foreground">
                  使用条款
                </Link>
              </li>
            </ul>
          </div>
          
          {/* Categories */}
          <div className="space-y-4">
            <h4 className="text-sm font-semibold">热门分类</h4>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/category/react" className="text-muted-foreground hover:text-foreground">
                  React
                </Link>
              </li>
              <li>
                <Link href="/category/vue" className="text-muted-foreground hover:text-foreground">
                  Vue
                </Link>
              </li>
              <li>
                <Link href="/category/javascript" className="text-muted-foreground hover:text-foreground">
                  JavaScript
                </Link>
              </li>
              <li>
                <Link href="/category/typescript" className="text-muted-foreground hover:text-foreground">
                  TypeScript
                </Link>
              </li>
            </ul>
          </div>
          
          {/* Resources */}
          <div className="space-y-4">
            <h4 className="text-sm font-semibold">资源</h4>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/tutorials" className="text-muted-foreground hover:text-foreground">
                  所有教程
                </Link>
              </li>
              <li>
                <Link href="/projects" className="text-muted-foreground hover:text-foreground">
                  实战项目
                </Link>
              </li>
              <li>
                <Link href="/tools" className="text-muted-foreground hover:text-foreground">
                  开发工具
                </Link>
              </li>
              <li>
                <Link href="/blog" className="text-muted-foreground hover:text-foreground">
                  技术博客
                </Link>
              </li>
            </ul>
          </div>
        </div>
        
        <div className="mt-8 border-t pt-8 text-center text-sm text-muted-foreground">
          <p>
            © 2024 {siteConfig.name}. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  )
}
