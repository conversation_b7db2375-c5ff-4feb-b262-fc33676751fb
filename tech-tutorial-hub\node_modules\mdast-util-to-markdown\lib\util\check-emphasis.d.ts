/**
 * @import {Options, State} from 'mdast-util-to-markdown'
 */
/**
 * @param {State} state
 * @returns {Exclude<Options['emphasis'], null | undefined>}
 */
export function checkEmphasis(state: State): Exclude<Options["emphasis"], null | undefined>;
import type { State } from 'mdast-util-to-markdown';
import type { Options } from 'mdast-util-to-markdown';
//# sourceMappingURL=check-emphasis.d.ts.map